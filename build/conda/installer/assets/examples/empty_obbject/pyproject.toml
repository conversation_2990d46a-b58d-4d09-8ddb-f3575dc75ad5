[tool.poetry]
name = "empty-obbject"
version = "0.0.1"
description = "An empty OBBject extension"
authors = ["OpenBB Team <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "empty_obbject" }]

[tool.poetry.dependencies]
python = "^3.9.21,<3.13"
openbb-core = "^1.4.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_obbject_extension"]
empty = "empty_obbject:ext"
