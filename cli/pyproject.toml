[tool.poetry]
name = "openbb-cli"
version = "1.1.10"
description = "Investment Research for Everyone, Anywhere."
authors = ["OpenBB <<EMAIL>>"]
packages = [{ include = "openbb_cli" }]
license = "AGPL-3.0-only"
readme = "README.md"
homepage = "https://openbb.co"
repository = "https://github.com/OpenBB-finance/OpenBB"
documentation = "https://docs.openbb.co/cli"

[tool.poetry.scripts]
openbb = 'openbb_cli.cli:main'

[tool.poetry.dependencies]
python = "^3.9.21,<3.13"

# OpenBB dependencies
openbb = { version = "^4.4.5", extras = ["all"] }

# CLI dependencies
prompt-toolkit = "^3.0.50"
rich = "^14.0.0"
python-dotenv = "^1.0.1"
openpyxl = "^3.1.5"
pywry = "^0.6.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
