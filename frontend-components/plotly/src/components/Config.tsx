export const ICONS = {
  sunIcon: {
    viewBox: "0 0 16 16",
    width: 16,
    height: 16,
    path: "M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z",
  },
  moonIcon: {
    viewBox: "0 0 25 25",
    width: 25,
    height: 25,
    path: "M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z",
  },
  plotCsv: {
    width: 900,
    height: 900,
    path: "M170.666667 106.666667l0.192 736H906.666667v64H149.546667c-23.552 0-42.666667-19.093333-42.666667-42.666667L106.666667 106.666667h64z m686.506666 454.144l13.653334 16.362666a21.333333 21.333333 0 0 1-2.666667 30.058667l-171.157333 143.146667a21.333333 21.333333 0 0 1-21.546667 3.477333l-229.973333-91.285333-113.834667 94.997333a21.333333 21.333333 0 0 1-30.037333-2.709333l-13.653334-16.362667a21.333333 21.333333 0 0 1 2.688-30.058667l133.312-111.274666a21.333333 21.333333 0 0 1 21.546667-3.456l229.930667 91.264 151.68-126.826667a21.333333 21.333333 0 0 1 30.037333 2.666667z m-1.621333-417.962667l16.896 13.013333a21.333333 21.333333 0 0 1 3.925333 29.888L685.802667 433.706667a21.333333 21.333333 0 0 1-20.202667 8.085333l-226.794667-35.413333-150.186666 222.357333a21.333333 21.333333 0 0 1-27.477334 7.018667l-2.133333-1.28-17.685333-11.946667a21.333333 21.333333 0 0 1-5.738667-29.610667l165.354667-244.821333a21.333333 21.333333 0 0 1 20.992-9.130667L650.453333 374.613333l175.146667-227.882666a21.333333 21.333333 0 0 1 29.930667-3.904z",
  },
  addText: {
    path: "M896 928H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32z m-736-64h704v-704h-704z M704 352H320a32 32 0 0 1 0-64h384a32 32 0 0 1 0 64z M512 736a32 32 0 0 1-32-32V320a32 32 0 0 1 64 0v384a32 32 0 0 1-32 32z",
    width: 950,
    height: 950,
  },
  changeTitle: {
    path: "M122.368 165.888h778.24c-9.216 0-16.384-7.168-16.384-16.384v713.728c0-9.216 7.168-16.384 16.384-16.384h-778.24c9.216 0 16.384 7.168 16.384 16.384V150.016c0 8.192-6.656 15.872-16.384 15.872z m-32.768 684.544c0 26.112 20.992 47.104 47.104 47.104h750.08c26.112 0 47.104-20.992 47.104-47.104V162.304c0-26.112-20.992-47.104-47.104-47.104H136.704c-26.112 0-47.104 20.992-47.104 47.104v688.128z M244.736 656.896h534.016v62.464H244.736z M373.76 358.4H307.2v219.136h-45.568V358.4H192v-41.472H373.76V358.4zM403.968 316.928h44.032v50.176h-44.032v-50.176z m0 67.072h44.032v194.048h-44.032V384zM576.512 541.184l8.704 31.744c-13.312 5.12-26.624 8.192-38.912 8.704-32.768 1.024-48.64-15.36-48.128-48.128V422.912h-26.624V384h26.624v-46.592l44.032-21.504V384h36.352v38.912h-36.352V532.48c-1.024 10.24 3.072 14.848 11.264 13.824 5.12 0 12.8-1.536 23.04-5.12zM619.008 316.928h44.032v260.608h-44.032V316.928zM813.056 509.952l41.472 12.8c-11.776 40.96-37.888 61.44-78.336 60.416-52.736-1.536-80.384-34.304-81.92-98.304 2.56-67.072 29.696-102.4 81.92-105.984 52.224 1.536 78.336 36.864 79.36 105.984v13.824h-117.248c3.584 30.208 15.872 45.568 37.888 46.592 19.968 0.512 32.256-11.264 36.864-35.328z m-72.704-51.712h70.656c-1.024-25.088-12.288-38.4-33.792-38.912-21.504 0.512-33.792 13.824-36.864 38.912z",
    width: 920,
    height: 900,
  },
  changeColor: {
    path: "M8 3C5.79 3 4 4.79 4 7V14C4 15.1 4.9 16 6 16H9V20C9 21.1 9.9 22 11 22H13C14.1 22 15 21.1 15 20V16H18C19.1 16 20 15.1 20 14V3H8M8 5H12V7H14V5H15V9H17V5H18V10H6V7C6 5.9 6.9 5 8 5M6 14V12H18V14H6Z",
    width: 22,
    height: 22,
  },
  uploadImage: {
    path: "M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5",
    width: 1024,
    height: 1024,
  },
  downloadCsv: {
    path: `M486.2,196.121h-13.164V132.59c0-0.399-0.064-0.795-0.116-1.2c-0.021-2.52-0.824-5-2.551-6.96L364.656,3.677
		c-0.031-0.034-0.064-0.044-0.085-0.075c-0.629-0.707-1.364-1.292-2.141-1.796c-0.231-0.157-0.462-0.286-0.704-0.419
		c-0.672-0.365-1.386-0.672-2.121-0.893c-0.199-0.052-0.377-0.134-0.576-0.188C358.229,0.118,357.4,0,356.562,0H96.757
		C84.893,0,75.256,9.649,75.256,21.502v174.613H62.093c-16.972,0-30.733,13.756-30.733,30.73v159.81
		c0,16.966,13.761,30.736,30.733,30.736h13.163V526.79c0,11.854,9.637,21.501,21.501,21.501h354.777
		c11.853,0,21.502-9.647,21.502-21.501V417.392H486.2c16.966,0,30.729-13.764,30.729-30.731v-159.81
		C516.93,209.872,503.166,196.121,486.2,196.121z M96.757,21.502h249.053v110.006c0,5.94,4.818,10.751,10.751,10.751h94.973v53.861
		H96.757V21.502z M258.618,313.18c-26.68-9.291-44.063-24.053-44.063-47.389c0-27.404,22.861-48.368,60.733-48.368
		c18.107,0,31.447,3.811,40.968,8.107l-8.09,29.3c-6.43-3.107-17.862-7.632-33.59-7.632c-15.717,0-23.339,7.149-23.339,15.485
		c0,10.247,9.047,14.769,29.78,22.632c28.341,10.479,41.681,25.239,41.681,47.874c0,26.909-20.721,49.786-64.792,49.786
		c-18.338,0-36.449-4.776-45.497-9.77l7.38-30.016c9.772,5.014,24.775,10.006,40.264,10.006c16.671,0,25.488-6.908,25.488-17.396
		C285.536,325.789,277.909,320.078,258.618,313.18z M69.474,302.692c0-54.781,39.074-85.269,87.654-85.269
		c18.822,0,33.113,3.811,39.549,7.149l-7.392,28.816c-7.38-3.084-17.632-5.939-30.491-5.939c-28.822,0-51.206,17.375-51.206,53.099
		c0,32.158,19.051,52.4,51.456,52.4c10.947,0,23.097-2.378,30.241-5.238l5.483,28.346c-6.672,3.34-21.674,6.919-41.208,6.919
		C98.06,382.976,69.474,348.424,69.474,302.692z M451.534,520.962H96.757v-103.57h354.777V520.962z M427.518,380.583h-42.399
		l-51.45-160.536h39.787l19.526,67.894c5.479,19.046,10.479,37.386,14.299,57.397h0.709c4.048-19.298,9.045-38.352,14.526-56.693
		l20.487-68.598h38.599L427.518,380.583z`,
    width: 550,
    height: 550,
    transform: "translate(4, 0)",
  },
  downloadImage: {
    path: "M22.71,6.29a1,1,0,0,0-1.42,0L20,7.59V2a1,1,0,0,0-2,0V7.59l-1.29-1.3a1,1,0,0,0-1.42,1.42l3,3a1,1,0,0,0,.33.21.94.94,0,0,0,.76,0,1,1,0,0,0,.33-.21l3-3A1,1,0,0,0,22.71,6.29ZM19,13a1,1,0,0,0-1,1v.38L16.52,12.9a2.79,2.79,0,0,0-3.93,0l-.7.7L9.41,11.12a2.85,2.85,0,0,0-3.93,0L4,12.6V7A1,1,0,0,1,5,6h8a1,1,0,0,0,0-2H5A3,3,0,0,0,2,7V19a3,3,0,0,0,3,3H17a3,3,0,0,0,3-3V14A1,1,0,0,0,19,13ZM5,20a1,1,0,0,1-1-1V15.43l2.9-2.9a.79.79,0,0,1,1.09,0l3.17,3.17,0,0L15.46,20Zm13-1a.89.89,0,0,1-.18.53L13.31,15l.7-.7a.77.77,0,0,1,1.1,0L18,17.21Z",
    width: 21,
    height: 21,
    transform: "translate(-2, -2)",
  },
};

export const DARK_CHARTS_TEMPLATE = {
  line: {
    "up_color": "#0074D9",
    "down_color": "#FF4136",
    "color": "#111111",
    "width": 1.5
  },
  data: {
    candlestick: [
      {
        decreasing: {
          fillcolor: "#e4003a",
          line: {
            color: "#e4003a",
          },
        },
        increasing: {
          fillcolor: "#00ACFF",
          line: {
            color: "#00ACFF",
          },
        },
        type: "candlestick",
      },
    ],
  },
  layout: {
    annotationdefaults: {
      showarrow: false,
    },
    autotypenumbers: "strict",
    colorway: [
      "#1f77b4",
      "#ff7f0e",
      "#2ca02c",
      "#d62728",
      "#9467bd",
      "#8c564b",
      "#e377c2",
      "#bcbd22",
      "#17becf",
      "#aec7e8",
      "#ffbb78",
      "#ff9896",
      "#c5b0d5",
      "#f7b6d2",
      "#dbdb8d",
      "#9edae5"
    ],
    dragmode: "pan",
    font: {
      family: "Arial, Helvetica, sans-serif",
      size: 16,
    },
    hoverlabel: {
      align: "left",
      font: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
    },
    mapbox: {
      style: "dark",
    },
    hovermode: "x",
    legend: {
      bgcolor: "rgba(0, 0, 0, 0)",
      x: 1,
      xanchor: "right",
      y: 0.99,
      yanchor: "bottom",
      font: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
    },
    paper_bgcolor: "#000000",
    plot_bgcolor: "#000000",
    xaxis: {
      automargin: true,
      autorange: true,
      rangeslider: {
        visible: false,
      },
      showgrid: true,
      showline: true,
      tickfont: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
      zeroline: false,
      tick0: 1,
      title: {
        standoff: 20,
        text: "",
        font: {
          family: "Arial, Helvetica, sans-serif",
          size: 16,
        },
      },
      gridcolor: "#283442",
      linecolor: "#F5EFF3",
      mirror: true,
      ticks: "outside",
    },
    yaxis: {
      anchor: "x",
      automargin: true,
      fixedrange: false,
      zeroline: false,
      showgrid: true,
      showline: true,
      side: "right",
      tick0: 0.5,
      tickfont: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
      title: {
        standoff: 20,
        text: "",
        font: {
          family: "Arial, Helvetica, sans-serif",
          size: 16,
        },
      },
      gridcolor: "#283442",
      linecolor: "#F5EFF3",
      mirror: true,
      ticks: "outside",
    },
  },
};

export const LIGHT_CHARTS_TEMPLATE = {
  line: {
    "up_color": "#0074D9",
    "down_color": "#FF4136",
    "color": "#111111",
    "width": 1.5
  },
  data: {
    barpolar: [
      {
        marker: {
          line: {
            color: "white",
            width: 0.5,
          },
          pattern: {
            fillmode: "overlay",
            size: 10,
            solidity: 0.2,
          },
        },
        type: "barpolar",
      },
    ],
    bar: [
      {
        error_x: {
          color: "#2a3f5f",
        },
        error_y: {
          color: "#2a3f5f",
        },
        marker: {
          line: {
            color: "white",
            width: 0.5,
          },
          pattern: {
            fillmode: "overlay",
            size: 10,
            solidity: 0.2,
          },
        },
        type: "bar",
      },
    ],
    carpet: [
      {
        aaxis: {
          endlinecolor: "#2a3f5f",
          gridcolor: "#C8D4E3",
          linecolor: "#C8D4E3",
          minorgridcolor: "#C8D4E3",
          startlinecolor: "#2a3f5f",
        },
        baxis: {
          endlinecolor: "#2a3f5f",
          gridcolor: "#C8D4E3",
          linecolor: "#C8D4E3",
          minorgridcolor: "#C8D4E3",
          startlinecolor: "#2a3f5f",
        },
        type: "carpet",
      },
    ],
    choropleth: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        type: "choropleth",
      },
    ],
    contourcarpet: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        type: "contourcarpet",
      },
    ],
    contour: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        colorscale: [
          [0.0, "#0d0887"],
          [0.1111111111111111, "#46039f"],
          [0.2222222222222222, "#7201a8"],
          [0.3333333333333333, "#9c179e"],
          [0.4444444444444444, "#bd3786"],
          [0.5555555555555556, "#d8576b"],
          [0.6666666666666666, "#ed7953"],
          [0.7777777777777778, "#fb9f3a"],
          [0.8888888888888888, "#fdca26"],
          [1.0, "#f0f921"],
        ],
        type: "contour",
      },
    ],
    heatmap: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        colorscale: [
          [0.0, "#0d0887"],
          [0.1111111111111111, "#46039f"],
          [0.2222222222222222, "#7201a8"],
          [0.3333333333333333, "#9c179e"],
          [0.4444444444444444, "#bd3786"],
          [0.5555555555555556, "#d8576b"],
          [0.6666666666666666, "#ed7953"],
          [0.7777777777777778, "#fb9f3a"],
          [0.8888888888888888, "#fdca26"],
          [1.0, "#f0f921"],
        ],
        type: "heatmap",
      },
    ],
    histogram2dcontour: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        colorscale: [
          [0.0, "#0d0887"],
          [0.1111111111111111, "#46039f"],
          [0.2222222222222222, "#7201a8"],
          [0.3333333333333333, "#9c179e"],
          [0.4444444444444444, "#bd3786"],
          [0.5555555555555556, "#d8576b"],
          [0.6666666666666666, "#ed7953"],
          [0.7777777777777778, "#fb9f3a"],
          [0.8888888888888888, "#fdca26"],
          [1.0, "#f0f921"],
        ],
        type: "histogram2dcontour",
      },
    ],
    histogram2d: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        colorscale: [
          [0.0, "#0d0887"],
          [0.1111111111111111, "#46039f"],
          [0.2222222222222222, "#7201a8"],
          [0.3333333333333333, "#9c179e"],
          [0.4444444444444444, "#bd3786"],
          [0.5555555555555556, "#d8576b"],
          [0.6666666666666666, "#ed7953"],
          [0.7777777777777778, "#fb9f3a"],
          [0.8888888888888888, "#fdca26"],
          [1.0, "#f0f921"],
        ],
        type: "histogram2d",
      },
    ],
    histogram: [
      {
        marker: {
          pattern: {
            fillmode: "overlay",
            size: 10,
            solidity: 0.2,
          },
        },
        type: "histogram",
      },
    ],
    mesh3d: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        type: "mesh3d",
      },
    ],
    parcoords: [
      {
        line: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "parcoords",
      },
    ],
    pie: [
      {
        automargin: true,
        type: "pie",
      },
    ],
    scatter3d: [
      {
        line: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scatter3d",
      },
    ],
    scattercarpet: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scattercarpet",
      },
    ],
    scattergeo: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scattergeo",
      },
    ],
    scattergl: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scattergl",
      },
    ],
    scattermapbox: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scattermapbox",
      },
    ],
    scatterpolargl: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scatterpolargl",
      },
    ],
    scatterpolar: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scatterpolar",
      },
    ],
    scatter: [
      {
        fillpattern: {
          fillmode: "overlay",
          size: 10,
          solidity: 0.2,
        },
        type: "scatter",
      },
    ],
    scatterternary: [
      {
        marker: {
          colorbar: {
            outlinewidth: 0,
            ticks: "",
          },
        },
        type: "scatterternary",
      },
    ],
    surface: [
      {
        colorbar: {
          outlinewidth: 0,
          ticks: "",
        },
        colorscale: [
          [0.0, "#0d0887"],
          [0.1111111111111111, "#46039f"],
          [0.2222222222222222, "#7201a8"],
          [0.3333333333333333, "#9c179e"],
          [0.4444444444444444, "#bd3786"],
          [0.5555555555555556, "#d8576b"],
          [0.6666666666666666, "#ed7953"],
          [0.7777777777777778, "#fb9f3a"],
          [0.8888888888888888, "#fdca26"],
          [1.0, "#f0f921"],
        ],
        type: "surface",
      },
    ],
    table: [
      {
        cells: {
          fill: {
            color: "#EBF0F8",
          },
          line: {
            color: "white",
          },
        },
        header: {
          fill: {
            color: "#C8D4E3",
          },
          line: {
            color: "white",
          },
        },
        type: "table",
      },
    ],
    candlestick: [
      {
        "decreasing": {
            "fillcolor": "#e4003a",
            "line": {
                "color": "#e4003a"
            }
        },
        "increasing": {
            "fillcolor": "#00ACFF",
            "line": {
                "color": "#00ACFF"
            }
        },
        "type": "candlestick"
      }
    ],
  },
  layout: {
    annotationdefaults: {
      arrowcolor: "#2a3f5f",
      arrowhead: 0,
      arrowwidth: 1,
      showarrow: false,
    },
    autotypenumbers: "strict",
    coloraxis: {
      colorbar: {
        outlinewidth: 0,
        ticks: "",
      },
    },
    colorscale: {
      diverging: [
        [0, "#8e0152"],
        [0.1, "#c51b7d"],
        [0.2, "#de77ae"],
        [0.3, "#f1b6da"],
        [0.4, "#fde0ef"],
        [0.5, "#f7f7f7"],
        [0.6, "#e6f5d0"],
        [0.7, "#b8e186"],
        [0.8, "#7fbc41"],
        [0.9, "#4d9221"],
        [1, "#276419"],
      ],
      sequential: [
        [0.0, "#0d0887"],
        [0.1111111111111111, "#46039f"],
        [0.2222222222222222, "#7201a8"],
        [0.3333333333333333, "#9c179e"],
        [0.4444444444444444, "#bd3786"],
        [0.5555555555555556, "#d8576b"],
        [0.6666666666666666, "#ed7953"],
        [0.7777777777777778, "#fb9f3a"],
        [0.8888888888888888, "#fdca26"],
        [1.0, "#f0f921"],
      ],
      sequentialminus: [
        [0.0, "#0d0887"],
        [0.1111111111111111, "#46039f"],
        [0.2222222222222222, "#7201a8"],
        [0.3333333333333333, "#9c179e"],
        [0.4444444444444444, "#bd3786"],
        [0.5555555555555556, "#d8576b"],
        [0.6666666666666666, "#ed7953"],
        [0.7777777777777778, "#fb9f3a"],
        [0.8888888888888888, "#fdca26"],
        [1.0, "#f0f921"],
      ],
    },
    colorway: [
      "#1f77b4",
      "#ff7f0e",
      "#2ca02c",
      "#d62728",
      "#9467bd",
      "#8c564b",
      "#e377c2",
      "#bcbd22",
      "#17becf",
      "#aec7e8",
      "#ffbb78",
      "#ff9896",
      "#c5b0d5",
      "#f7b6d2",
      "#dbdb8d",
      "#9edae5"
    ],
    font: {
      family: "Arial, Helvetica, sans-serif",
      size: 16,
    },
    geo: {
      bgcolor: "white",
      lakecolor: "white",
      landcolor: "white",
      showlakes: true,
      showland: true,
      subunitcolor: "#C8D4E3",
    },
    hoverlabel: {
      align: "left",
      font: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
    },
    hovermode: "x",
    mapbox: {
      style: "light",
    },
    paper_bgcolor: "#FFFFFF",
    plot_bgcolor: "#FFFFFF",
    polar: {
      angularaxis: {
        gridcolor: "#EBF0F8",
        linecolor: "#EBF0F8",
        ticks: "",
      },
      bgcolor: "white",
      radialaxis: {
        gridcolor: "#EBF0F8",
        linecolor: "#EBF0F8",
        ticks: "",
      },
    },
    scene: {
      xaxis: {
        backgroundcolor: "white",
        gridcolor: "#DFE8F3",
        gridwidth: 2,
        linecolor: "#EBF0F8",
        showbackground: true,
        ticks: "",
        zerolinecolor: "#EBF0F8",
      },
      yaxis: {
        backgroundcolor: "white",
        gridcolor: "#DFE8F3",
        gridwidth: 2,
        linecolor: "#EBF0F8",
        showbackground: true,
        ticks: "",
        zerolinecolor: "#EBF0F8",
      },
      zaxis: {
        backgroundcolor: "white",
        gridcolor: "#DFE8F3",
        gridwidth: 2,
        linecolor: "#EBF0F8",
        showbackground: true,
        ticks: "",
        zerolinecolor: "#EBF0F8",
      },
    },
    shapedefaults: {
      line: {
        color: "#2a3f5f",
      },
    },
    ternary: {
      aaxis: {
        gridcolor: "#DFE8F3",
        linecolor: "#A2B1C6",
        ticks: "",
      },
      baxis: {
        gridcolor: "#DFE8F3",
        linecolor: "#A2B1C6",
        ticks: "",
      },
      bgcolor: "white",
      caxis: {
        gridcolor: "#DFE8F3",
        linecolor: "#A2B1C6",
        ticks: "",
      },
    },
    title: {
      x: 0.05,
    },
    xaxis: {
      automargin: true,
      autorange: true,
      rangeslider: {
        visible: false
      },
      showgrid: true,
      showline: true,
      tickfont: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
      zeroline: false,
      tick0: 1,
      title: {
        standoff: 20,
        font: {
          family: "Arial, Helvetica, sans-serif",
          size: 16,
        },
      },
      gridcolor: "#283442",
      linecolor: "#A9A9A9",
      mirror: true,
      ticks: "outside"
    },
    yaxis: {
      anchor: "x",
      automargin: true,
      fixedrange: false,
      zeroline: false,
      showgrid: true,
      showline: true,
      side: "right",
      tick0: 0.5,
      tickfont: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
      title: {
        standoff: 20,
        font: {
          family: "Arial, Helvetica, sans-serif",
          size: 16,
        },
      },
      gridcolor: "rgba(128, 128, 128, 0.33)",
      linecolor: "#A9A9A9",
      mirror: true,
      ticks: "outside"
    },
    dragmode: "pan",
    legend: {
      bgcolor: "rgba(255, 255, 255, 0)",
      x: 1,
      xanchor: "right",
      y: 1.02,
      yanchor: "bottom",
      font: {
        family: "Arial, Helvetica, sans-serif",
        size: 14,
      },
    },
  },
};
