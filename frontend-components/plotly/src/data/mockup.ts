export const plotlyMockup = {
	collect_logs: false,
	command_location: "/stocks/ta/bbands",
	data: [
		{
			connectgaps: true,
			hovertemplate: "%{y}<extra></extra>",
			name: "QQQ Bollinger Bands Close         ",
			type: "scatter",
			x: [
				"2020-04-20T00:00:00",
				"2020-04-21T00:00:00",
				"2020-04-22T00:00:00",
				"2020-04-23T00:00:00",
				"2020-04-24T00:00:00",
				"2020-04-27T00:00:00",
				"2020-04-28T00:00:00",
				"2020-04-29T00:00:00",
				"2020-04-30T00:00:00",
				"2020-05-01T00:00:00",
				"2020-05-04T00:00:00",
				"2020-05-05T00:00:00",
				"2020-05-06T00:00:00",
				"2020-05-07T00:00:00",
				"2020-05-08T00:00:00",
				"2020-05-11T00:00:00",
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
			],
			xaxis: "x",
			y: [
				209.1029815673828, 201.38719177246097, 207.36322021484375,
				206.9208984375, 210.1841735839844, 211.874755859375, 207.8841552734375,
				215.25592041015625, 215.16749572753903, 209.1029815673828,
				211.54055786132812, 213.93885803222656, 215.25592041015625,
				218.0277099609375, 221.0157470703125, 222.9913787841797,
				218.32260131835935, 215.59011840820312, 218.03756713867188,
				219.4529571533203, 223.5418243408203, 222.9815673828125,
				227.43409729003903, 224.9571990966797, 225.73370361328125,
				225.1242828369141, 226.3529052734375, 226.05809020996097,
				229.3704376220703, 230.05845642089844, 231.6016387939453,
				232.64352416992188, 231.01190185546875, 235.59222412109375,
				237.42041015625, 239.14051818847656, 242.01058959960935,
				230.0191650390625, 231.8473663330078, 234.66827392578125,
				238.69818115234375, 239.45501708984375, 240.1037445068359,
				240.064453125, 242.9434356689453, 245.0111083984375, 239.9600830078125,
				242.24435424804688, 236.52377319335935, 239.1034393310547,
				243.79022216796875, 246.6357421875, 248.3095703125, 254.4141845703125,
				252.6615447998047, 255.9895782470703, 258.1458435058594,
				259.9083251953125, 254.56187438964844, 256.36370849609375,
				256.8855895996094, 255.1329803466797, 255.4283447265625,
				262.67510986328125, 259.9378967285156, 260.8535461425781,
				254.0400848388672, 251.62774658203125, 256.1175537109375,
				252.8585205078125, 255.77293395996097, 257.1119689941406,
				261.7003173828125, 265.2350769042969, 266.2196960449219,
				266.87933349609375, 270.4141845703125, 267.2929382324219,
				266.1507873535156, 261.10955810546875, 267.6768798828125,
				268.287353515625, 267.9723205566406, 271.08367919921875,
				273.6929016113281, 271.8517150878906, 275.62274169921875,
				277.5328674316406, 279.265869140625, 281.46148681640625,
				287.46759033203125, 286.5716247558594, 288.0289306640625,
				290.3427734375, 295.3051452636719, 298.10150146484375,
				282.9876708984375, 279.21661376953125, 265.7963562011719,
				273.60430908203125, 268.1495666503906, 266.2886047363281,
				270.9261474609375, 274.76611328125, 270.38458251953125, 266.16064453125,
				262.76373291015625, 263.39385986328125, 268.287353515625,
				260.09539794921875, 261.3064880371094, 267.3815002441406,
				272.9347839355469, 271.7040100097656, 273.5649108886719,
				277.9071044921875, 270.0892028808594, 275.84918212890625,
				270.9261474609375, 275.6129150390625, 277.0799865722656,
				281.3138427734375, 289.99810791015625, 289.9881591796875,
				287.56610107421875, 285.6362609863281, 284.07073974609375,
				279.43316650390625, 280.0338439941406, 279.81719970703125,
				279.8073425292969, 280.3587341308594, 276.1544189453125,
				278.3106994628906, 267.4602966308594, 272.13726806640625,
				265.2350769042969, 265.82586669921875, 270.4239807128906,
				282.495361328125, 289.8602600097656, 290.0768737792969,
				284.14947509765625, 279.05908203125, 285.3014831542969,
				283.9623718261719, 286.4535217285156, 288.6885986328125,
				287.7728576660156, 285.6460876464844, 287.8811950683594,
				285.9119567871094, 285.9218444824219, 289.93896484375, 291.72119140625,
				294.4091796875, 295.0097961425781, 298.79071044921875,
				299.17474365234375, 299.59808349609375, 300.8190002441406,
				302.52239990234375, 303.54632568359375, 296.6737976074219,
				297.8652038574219, 297.2054748535156, 299.36175537109375,
				302.5617980957031, 304.2257385253906, 306.2147216796875,
				305.2891540527344, 304.718017578125, 305.54656982421875,
				304.0078430175781, 305.3493347167969, 308.4268493652344,
				308.70306396484375, 308.712890625, 309.47247314453125,
				305.1026916503906, 307.61798095703125, 303.3567810058594,
				310.69561767578125, 314.6905212402344, 310.1431884765625,
				309.6500244140625, 311.74114990234375, 310.0741882324219,
				307.61798095703125, 312.1061096191406, 319.36602783203125,
				321.9208068847656, 320.9936218261719, 323.6470031738281,
				324.1204833984375, 315.0850830078125, 316.9493713378906,
				310.28131103515625, 318.034423828125, 323.2228088378906,
				321.94049072265625, 325.74798583984375, 326.852783203125,
				329.0425720214844, 328.9735107421875, 328.2239074707031,
				330.02899169921875, 331.87353515625, 330.9759521484375,
				329.3877868652344, 327.94769287109375, 326.5173645019531,
				318.0541076660156, 317.1072082519531, 319.7210998535156,
				308.5748291015625, 309.8670349121094, 319.1884460449219,
				314.0690612792969, 304.9547424316406, 299.96356201171875,
				304.4812927246094, 295.86016845703125, 307.5292053222656,
				306.6513366699219, 313.7139587402344, 311.1690368652344,
				314.4931945800781, 316.21942138671875, 317.5214538574219,
				307.79559326171875, 308.8805847167969, 314.67315673828125,
				313.30035400390625, 308.01641845703125, 307.4830322265625,
				312.0953674316406, 312.00653076171875, 310.43609619140625,
				315.1867370605469, 320.55950927734375, 326.9594421386719,
				326.7322692871094, 327.5223388671875, 330.9395751953125,
				332.94451904296875, 332.510009765625, 336.3914794921875,
				332.3519592285156, 337.3889465332031, 337.78399658203125,
				334.69268798828125, 332.25323486328125, 335.09759521484375,
				331.0581359863281, 335.2260437011719, 337.40869140625,
				335.94696044921875, 334.81121826171875, 336.01611328125,
				333.813720703125, 332.0358581542969, 326.0606689453125,
				324.9643859863281, 327.41375732421875, 330.07049560546875,
				321.73480224609375, 321.29034423828125, 312.974365234375,
				315.39410400390625, 322.3570251464844, 320.4014892578125,
				318.23858642578125, 318.60394287109375, 324.7668151855469,
				322.9693603515625, 328.4013671875, 328.855712890625, 330.00140380859375,
				328.7668151855469, 329.8038024902344, 328.7075500488281,
				329.3494873046875, 325.92236328125, 331.4532470703125,
				332.44085693359375, 332.598876953125, 332.6778869628906,
				336.1445007324219, 337.02349853515625, 340.2531433105469,
				338.0309143066406, 336.7963562011719, 341.0728454589844,
				338.3963623046875, 340.4994812011719, 343.6734924316406,
				343.8415832519531, 345.9674987792969, 345.54229736328125,
				349.7446594238281, 351.01031494140625, 350.45654296875,
				350.59503173828125, 354.6194152832031, 356.1519775390625,
				356.90350341796875, 354.7479553222656, 356.9627990722656,
				358.3570251464844, 358.3570251464844, 358.9997253417969,
				356.4783020019531, 353.591064453125, 350.6939392089844,
				354.76776123046875, 357.5066833496094, 359.8699035644531,
				364.0722351074219, 364.35894775390625, 360.3444519042969,
				361.728759765625, 362.37152099609375, 360.4828796386719,
				360.5126037597656, 362.69781494140625, 363.2218627929687,
				365.5257263183594, 363.92388916015625, 364.5962829589844,
				362.7274475097656, 362.1045227050781, 363.4096984863281,
				364.685302734375, 364.843505859375, 361.6299133300781,
				358.1493835449219, 359.8797302246094, 363.60748291015625,
				369.04583740234375, 370.1730651855469, 370.5982360839844,
				368.2449340820313, 371.8243103027344, 375.99700927734375,
				375.6905212402344, 376.3133850097656, 376.1354675292969,
				377.2922973632813, 377.8262939453125, 376.51116943359375,
				375.2158508300781, 372.3681640625, 372.1110229492187, 371.0531005859375,
				373.811767578125, 374.06884765625, 369.63909912109375,
				362.0011291503906, 362.4465942382813, 365.83203125, 369.2075500488281,
				369.5539855957031, 366.6041259765625, 356.2301025390625,
				355.6460876464844, 354.3394470214844, 356.5369567871094,
				349.0534362792969, 353.76531982421875, 356.0321044921875,
				359.2987976074219, 357.5071105957031, 354.7651062011719,
				353.5277404785156, 356.3587951660156, 362.9217834472656,
				365.2083740234375, 368.880859375, 371.6723327636719, 371.1873168945313,
				373.4541015625, 370.3162231445313, 374.1074523925781, 375.285400390625,
				376.1565246582031, 380.3338012695313, 382.2047119140625,
				383.5113220214844, 385.1149597167969, 389.2032165527344,
				394.1922607421875, 394.5683898925781, 394.0338439941406,
				391.3215637207031, 385.5703430175781, 386.6393737792969,
				390.7078247070313, 390.6187438964844, 393.40032958984375,
				393.6082458496094, 397.6864929199219, 399.9038391113281,
				395.26129150390625, 393.4596862792969, 394.726806640625,
				387.2432556152344, 395.6473693847656, 389.8367309570313,
				383.2044372558594, 385.9662780761719, 379.2548522949219,
				382.2937927246094, 393.80615234375, 395.5681457519531,
				389.7476501464844, 393.9843444824219, 388.2925109863281,
				384.2735595703125, 393.0340270996094, 382.9273376464844,
				381.016845703125, 377.32086181640625, 385.7654113769531,
				390.4634704589844, 393.4071960449219, 399.9091796875, 398.0556640625,
				397.9961853027344, 396.8068237304687, 394.3290100097656,
				398.1250915527344, 392.9611511230469, 380.8890075683594,
				380.621337890625, 376.4981994628906, 376.7459716796875,
				382.4054260253906, 383.9219055175781, 374.3176879882813,
				376.6468200683594, 367.27056884765625, 363.2366027832031,
				358.5187072753906, 348.5774841308594, 350.1732177734375,
				342.05572509765625, 341.5205078125, 338.0812072753906, 348.6865234375,
				359.8369140625, 362.28509521484375, 365.2288208007813,
				350.4210205078125, 354.841552734375, 351.987060546875,
				355.9417724609375, 363.48443603515625, 355.2578430175781,
				343.9884948730469, 344.41461181640625, 352.9781799316406,
				352.88897705078125, 342.3927307128906, 338.48760986328125,
				335.0879211425781, 326.50457763671875, 337.4765930175781,
				342.7098693847656, 343.7307434082031, 338.4677734375,
				344.14703369140625, 339.2309875488281, 334.3148498535156,
				321.98492431640625, 320.48828125, 332.0252990722656, 328.3382263183594,
				321.52899169921875, 315.3541564941406, 325.2458190917969,
				337.30810546875, 341.3916931152344, 348.3792419433594,
				347.4106750488281, 354.2382507324219, 349.1473693847656,
				356.90777587890625, 356.61004638671875, 362.1276245117187,
				368.3597412109375, 364.291015625, 359.77569580078125, 359.0909729003906,
				366.4841613769531, 358.3467102050781, 350.56646728515625,
				351.4000549316406, 346.48779296875, 338.2908020019531,
				336.8617858886719, 343.70916748046875, 335.84954833984375,
				336.1075439453125, 343.6198425292969, 338.60833740234375,
				331.6021423339844, 322.91888427734375, 327.0670166015625,
				314.7218933105469, 314.34478759765625, 325.5090026855469,
				310.86151123046875, 316.0516662597656, 316.38909912109375,
				327.0868835449219, 310.6134338378906, 306.8919982910156,
				294.88427734375, 298.46673583984375, 289.61474609375, 288.9300537109375,
				299.63775634765625, 296.1644592285156, 303.8355407714844,
				288.9201354980469, 287.37200927734375, 286.4788818359375,
				291.2422790527344, 285.0498352050781, 289.0392150878906,
				297.04766845703125, 306.7431945800781, 305.9294128417969,
				303.66680908203125, 311.98291015625, 303.86529541015625,
				304.8775329589844, 307.5072937011719, 305.2943115234375,
				297.1171569824219, 286.6376647949219, 273.3100891113281,
				273.8062438964844, 280.6437072753906, 269.32073974609375,
				272.5955810546875, 279.4729919433594, 279.06536865234375,
				283.2214660644531, 292.9256591796875, 290.77801513671875,
				281.9189758300781, 282.17742919921875, 278.6776123046875,
				280.5169982910156, 285.3193664550781, 287.14886474609375,
				293.2935485839844, 293.6614074707031, 287.38751220703125, 284.603515625,
				284.0168762207031, 285.0310363769531, 290.2013244628906,
				287.7454528808594, 296.59454345703125, 301.2975158691406,
				305.6226501464844, 300.263427734375, 298.55328369140625,
				292.6870422363281, 305.055908203125, 308.03875732421875,
				313.6564636230469, 313.467529296875, 312.53289794921875,
				321.0439758300781, 322.5453186035156, 319.91046142578125,
				318.87640380859375, 315.2671813964844, 324.0666198730469,
				322.2271423339844, 328.5011291503906, 331.1557922363281,
				330.3802795410156, 326.6119689941406, 327.3974609375,
				321.01409912109375, 312.562744140625, 312.3042297363281,
				313.20904541015625, 318.7471618652344, 305.6822814941406,
				302.6696472167969, 299.2989807128906, 297.55902099609375,
				297.6882629394531, 293.48248291015625, 291.37457275390625,
				297.2607116699219, 298.8018493652344, 305.3343200683594,
				308.96343994140625, 292.0208740234375, 294.3375244140625,
				289.43572998046875, 287.6658935546875, 289.9060363769531,
				287.5951843261719, 282.44549560546875, 278.9692077636719,
				274.4271240234375, 273.2916259765625, 273.40118408203125,
				278.8397216796875, 270.80145263671875, 266.2095642089844,
				272.4549255371094, 281.0211181640625, 280.8717346191406,
				278.6604309082031, 268.0423278808594, 265.3629150390625,
				261.7173156738281, 261.6276550292969, 267.763427734375,
				259.7151794433594, 268.2913513183594, 270.4129638671875,
				269.4268798828125, 268.0522766113281, 274.3374938964844,
				277.3555908203125, 283.09295654296875, 276.8376159667969,
				271.7975158691406, 280.11468505859375, 276.8575744628906,
				274.0287170410156, 264.6357727050781, 259.4661560058594,
				263.63970947265625, 266.5382385253906, 268.4806213378906,
				262.2850646972656, 281.6387023925781, 286.8282165527344,
				284.318115234375, 288.2525939941406, 284.318115234375,
				283.6905822753906, 283.7005615234375, 280.7820739746094,
				284.8261413574219, 287.684814453125, 285.79229736328125,
				281.5988464355469, 279.4672546386719, 292.20697021484375,
				292.5655517578125, 291.400146484375, 286.5094909667969,
				280.5728759765625, 279.4273986816406, 282.734375, 280.9314880371094,
				284.4575500488281, 287.5155029296875, 285.3839111328125,
				275.8017272949219, 273.1720886230469, 269.3330383300781,
				269.1233825683594, 273.02734375, 266.3476867675781, 266.94671630859375,
				263.1725769042969, 259.6979675292969, 266.0281677246094,
				265.868408203125, 264.0711975097656, 265.3292236328125,
				261.1756591796875, 268.3844909667969, 270.121826171875,
				272.40826416015625, 277.1209716796875, 278.61865234375,
				280.53570556640625, 281.1048278808594, 277.4504699707031,
				274.7247009277344, 282.2430419921875, 288.5133361816406,
				287.92425537109375, 287.2852478027344, 292.8865661621094,
				295.80206298828125, 289.8213195800781, 294.1645812988281,
				300.4548645019531, 311.2381591796875, 305.70672607421875,
				303.1207275390625, 309.4010009765625, 303.8995361328125,
				301.21368408203125, 299.23675537109375, 304.0293273925781,
				306.2758483886719, 308.6222229003906, 302.8311767578125,
				300.6944885253906, 293.57550048828125, 293.795166015625,
				296.3612060546875, 291.3988952636719, 293.48565673828125,
				293.1062316894531, 290.7499084472656, 293.1561584472656,
				299.2167663574219, 299.5562438964844, 295.8819274902344,
				297.3596496582031, 292.2076416015625, 288.1039733886719,
				290.24066162109375, 296.91033935546875, 298.4679260253906,
				306.33575439453125, 304.8879699707031, 305.9700012207031,
				310.3399963378906, 306.1199951171875, 309.75, 310.8900146484375,
				308.760009765625, 307.1199951171875, 312.7200012207031,
				315.67999267578125, 320.92999267578125, 320.1499938964844,
				319.07000732421875, 315.9200134277344, 318.04998779296875,
				317.8699951171875, 315.8299865722656, 313.0400085449219,
				319.1700134277344, 318.57000732421875, 318.8399963378906,
				318.8599853515625, 318.7099914550781, 316.2799987792969,
				316.6099853515625,
			],
			yaxis: "y",
			hoverlabel: {
				namelength: 25,
			},
		},
		{
			connectgaps: true,
			hovertemplate: "%{y}<extra></extra>",
			line: {
				color: "#00ACFF",
				width: 1,
			},
			mode: "lines",
			name: "BBU_15_4.0         ",
			opacity: 1,
			type: "scatter",
			x: [
				"2020-04-20T00:00:00",
				"2020-04-21T00:00:00",
				"2020-04-22T00:00:00",
				"2020-04-23T00:00:00",
				"2020-04-24T00:00:00",
				"2020-04-27T00:00:00",
				"2020-04-28T00:00:00",
				"2020-04-29T00:00:00",
				"2020-04-30T00:00:00",
				"2020-05-01T00:00:00",
				"2020-05-04T00:00:00",
				"2020-05-05T00:00:00",
				"2020-05-06T00:00:00",
				"2020-05-07T00:00:00",
				"2020-05-08T00:00:00",
				"2020-05-11T00:00:00",
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
			],
			xaxis: "x",
			y: [
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				230.8516485931606,
				234.6310321436788,
				232.93389781510416,
				232.33088150307836,
				231.6444695548925,
				232.00198350532543,
				234.05903789866673,
				233.73181971938493,
				237.17885606049435,
				238.6779408892371,
				237.96270247484856,
				237.1462699933216,
				237.05981245669523,
				236.7285826684238,
				238.26131615349033,
				240.15117450344425,
				242.46545696373613,
				243.99809178120333,
				242.70936227312205,
				243.81002899100625,
				245.44373338970453,
				248.5054302109666,
				251.97926652837893,
				251.84399658425167,
				251.22186506954375,
				250.9758335707256,
				251.30461416877623,
				251.68599329814023,
				251.43176083600815,
				251.76160515642425,
				252.9986100721367,
				255.04565663395985,
				254.94166769878387,
				254.52143932873832,
				254.42126611448657,
				254.48000021889337,
				255.56837978446023,
				257.3317806354271,
				257.60107335090595,
				261.4337133279963,
				263.54312552057996,
				267.62230295360195,
				271.85979547659895,
				275.9580221145928,
				276.8081454209502,
				278.2897827317206,
				279.7819107518521,
				279.3270850597223,
				279.04502688106936,
				277.7070630227961,
				274.56489343967803,
				272.7603786392985,
				270.2996663615897,
				268.76125039779714,
				268.6694099760033,
				268.6133302302405,
				268.61465512850486,
				268.4551423094646,
				269.22315794077355,
				272.27327889748,
				275.24203880421516,
				277.93072648943973,
				281.7197786236759,
				283.17796444864035,
				284.0111543337474,
				284.0715059708232,
				285.49387903008414,
				286.025729813096,
				284.7639801498052,
				285.54791213840565,
				284.9501900345077,
				283.6411596731157,
				283.5345488265943,
				285.656398495247,
				288.83780294557766,
				292.4942765265175,
				298.8315040564072,
				303.2839748658281,
				307.11837722437366,
				310.69252089157146,
				313.5401119073995,
				318.3224987560952,
				316.8406568673439,
				314.5096471597681,
				316.5324635010278,
				316.55013482586736,
				317.6661845917009,
				319.9117744008454,
				320.71058870559574,
				320.85840073933014,
				321.365261122582,
				321.206034745951,
				321.3537543730538,
				319.9013243666774,
				315.8071828616289,
				308.59695192712513,
				294.1506776478068,
				288.15791406428696,
				284.74455320375023,
				285.52490583211284,
				285.5086786080101,
				288.81013489833936,
				288.9709431446751,
				290.42395693269935,
				289.41130940619325,
				290.81101642514193,
				292.620898950355,
				295.12478892974553,
				301.8824133900661,
				307.4204216083697,
				307.92267985028104,
				306.4881573710121,
				305.89278340689003,
				305.5475793688264,
				304.78207274830703,
				304.2533552181979,
				304.247282338821,
				302.19301339982053,
				302.1330861480581,
				299.883496380418,
				303.56472211371715,
				304.6838389621808,
				308.1963051451018,
				307.4908698841414,
				304.17357269248515,
				302.124785439306,
				304.2920681443406,
				306.9528211009725,
				308.0130993870754,
				307.87644258327595,
				309.1809880903621,
				310.01326444586687,
				311.4503428015843,
				313.6919813434155,
				315.31506525451016,
				314.27415961865944,
				314.4741859664392,
				310.40266096880356,
				303.922595829711,
				297.9459080813816,
				299.04782169108387,
				301.25146354036656,
				303.41750145737916,
				307.53509471497506,
				308.89312996934177,
				311.5276471402214,
				313.5842240840783,
				316.2551484240246,
				319.0136449799569,
				318.84496159697477,
				317.906481784964,
				317.1255125688559,
				315.30732269515727,
				313.0899137022337,
				312.6273308730698,
				313.11895097148056,
				313.5083843690211,
				313.28426472085823,
				314.1496305967076,
				314.3646432667875,
				314.88220256442895,
				316.61314068820246,
				318.1811861013118,
				319.4754349009937,
				319.43065752632793,
				318.1136708228298,
				316.0359436623318,
				314.26457395720183,
				315.2182462899899,
				318.7875966413468,
				319.35428093752734,
				319.593563201359,
				320.2183273000112,
				320.2947587129529,
				319.5729243107416,
				319.8524479953515,
				324.57343567673456,
				329.6162907404149,
				332.81152364965646,
				336.72614229513755,
				339.1397612768883,
				338.7571685571638,
				336.72632313394143,
				336.7962626932826,
				337.1880555126316,
				338.56645000512447,
				338.82773221606834,
				340.5315539589002,
				341.5145209864403,
				341.2660726310193,
				341.8222443703262,
				343.22213072656086,
				345.1139662435898,
				347.44827404772894,
				349.0471250651302,
				349.95232053026666,
				348.93743433844605,
				347.8340786968779,
				343.1894258010011,
				343.6429131829187,
				344.42017611180324,
				350.4002008003347,
				353.64523459943945,
				353.39249708176317,
				353.26575943940395,
				355.55436322046165,
				358.6199207882584,
				357.676161020826,
				357.5493329772994,
				352.97850405066333,
				348.1033265535578,
				343.0137138105713,
				337.4228735086698,
				336.29191650032465,
				335.9857941951142,
				335.0274773895361,
				335.02147544511723,
				334.9622617083429,
				333.1842871124171,
				332.9783006203287,
				332.8006327984769,
				331.1966751597453,
				331.0009140957301,
				325.0794181384021,
				324.68060081971896,
				324.53533279723143,
				327.5200541534316,
				334.18023415367446,
				338.7877901097371,
				342.7874197085281,
				347.799034466619,
				352.2070198312844,
				355.2606726240921,
				360.03467694923233,
				361.9832728768312,
				363.6107630425308,
				363.54969638754113,
				362.7438132524607,
				360.06696368095413,
				355.04696360028134,
				349.7019439975303,
				346.4793044285769,
				346.4971916549358,
				345.2430583838892,
				343.3867987839427,
				343.0018575874333,
				342.89531224128433,
				343.02313820993066,
				345.915310026374,
				348.51174512415656,
				348.4794746375471,
				347.308183072077,
				349.6471997685606,
				351.603299913424,
				356.175804557403,
				358.4446024741328,
				357.1843076497389,
				354.81161603802656,
				352.7337013720127,
				350.31875650664915,
				346.312769821121,
				342.6805277002392,
				340.88611847605864,
				341.8592891765924,
				343.4758416674397,
				343.9239273698017,
				343.81139314588614,
				344.9844222704702,
				346.15690701779255,
				343.68374295961246,
				342.8922812316215,
				344.2815691314583,
				344.734429158579,
				343.4000801644012,
				342.47776023625454,
				344.2324591706272,
				346.2771097650773,
				347.86289339609533,
				348.6200459148403,
				351.1143807781625,
				351.6736879053583,
				352.8658133152495,
				354.6688833590226,
				355.92451807645455,
				355.578276107151,
				356.4918808139567,
				359.18037231342174,
				361.4524215193959,
				362.2525705151638,
				363.46837416200515,
				366.18895853818555,
				369.4790840257945,
				371.6353403903798,
				371.35308915859,
				372.61175423527135,
				372.4701972814686,
				372.058389070958,
				372.2820993696499,
				370.86118668474217,
				369.289134710508,
				367.1714091439759,
				366.4357756129971,
				366.3776193299731,
				366.60926121120326,
				368.7488820426501,
				371.299047654024,
				371.7881708459357,
				372.6210360377386,
				373.2681022208906,
				373.5133660452937,
				373.7449818499154,
				374.4782437426242,
				375.2648195881179,
				376.5792788575951,
				376.08526012254634,
				372.95747565637896,
				370.6785195504983,
				369.29390007232104,
				368.9135123464024,
				369.1184507852676,
				369.29245047341306,
				368.95042811766285,
				370.34284622230865,
				370.6887727481851,
				370.6394889108141,
				373.00123638702183,
				375.61840639563945,
				377.8681884840965,
				378.56816590769176,
				380.8455598760957,
				384.99683646521254,
				387.900212659779,
				390.2806926390448,
				392.1491259695157,
				394.1604445201682,
				395.9054709329924,
				395.9003604555788,
				393.00253353162793,
				389.04829791825847,
				386.0586400835537,
				385.53314336285325,
				385.1095808861287,
				384.7029025359417,
				383.9928035803663,
				389.29355821966647,
				391.79216052235967,
				392.0778806862426,
				391.2885201240362,
				390.3550840286524,
				389.0099133028537,
				390.7865329268654,
				391.6744016932596,
				392.28645260831024,
				392.0931404120763,
				393.93849665419486,
				393.2401472247778,
				390.18390365703186,
				385.84047985120174,
				383.14755658736186,
				382.9840513626035,
				382.7207656276855,
				380.8528913873395,
				377.84022586252553,
				375.11759859978633,
				376.6366446123682,
				382.31198454045614,
				386.3027287756576,
				390.3561873590021,
				392.38768359996254,
				393.30998926566247,
				395.1553106976024,
				397.0016291581872,
				400.55560052870953,
				403.4417063248736,
				404.5341047281303,
				403.76277623953285,
				404.274031984336,
				408.8011831527119,
				412.24009063695974,
				414.9393737288427,
				416.1373998644825,
				415.0205109208834,
				414.22203090037414,
				412.25759676374673,
				410.78492486561566,
				409.5563833789581,
				407.4397454239212,
				408.01499601569606,
				409.5150988691093,
				408.6030328561655,
				407.3823444911042,
				407.38646211088144,
				407.969100541413,
				408.2260745227291,
				408.11216674962816,
				410.04114870703535,
				409.92597907000857,
				412.88347094130427,
				414.1577774241736,
				414.5586115083128,
				415.034023332793,
				414.6656096167993,
				413.5637571502089,
				410.6183626007752,
				409.74547673515264,
				409.62633497855785,
				408.7812604729757,
				409.6271746923784,
				409.13931665016264,
				408.62758639134046,
				409.16781130359146,
				410.5467047263893,
				413.2952935212635,
				414.9304357301912,
				416.2435267770568,
				416.6292328195717,
				417.2445002289677,
				418.3999213876923,
				418.6551505565937,
				419.50847643476055,
				420.31067803724255,
				422.092326485424,
				423.2600231149774,
				422.0228290532022,
				422.1879278494718,
				424.4317130718304,
				424.71867012279097,
				424.95318677540024,
				425.6592861418219,
				426.13897245758153,
				429.13563655257326,
				429.07673392091215,
				427.577443848469,
				425.4436030485849,
				427.0820097590515,
				424.0391848016308,
				420.9481462453697,
				417.57715454378234,
				411.61285697909574,
				402.4445640480923,
				396.9963602509529,
				388.519795238344,
				384.707456629661,
				384.81150952032823,
				384.1325011487283,
				384.761913826458,
				385.266537557449,
				384.30694038654906,
				382.7752076729915,
				380.9109222727688,
				383.86477696172886,
				385.9792258607987,
				389.40887698317533,
				385.0294850231132,
				384.5705047099652,
				382.85254786086136,
				382.0073103569858,
				379.2997524025201,
				371.1712431787037,
				367.360250031015,
				371.83986801906656,
				374.9762476894761,
				370.3235585750618,
				364.7456200376498,
				365.0790091831321,
				367.8980189561767,
				367.7073167800239,
				368.4623829091803,
				369.53636587306585,
				371.9654670182502,
				373.4588209140573,
				379.52118664036345,
				381.28643469423577,
				387.45215853578907,
				392.9631708369952,
				398.35349476351234,
				404.1551368732148,
				408.583371013664,
				409.5597967018492,
				406.58856079088395,
				398.84052630274783,
				390.15516298750623,
				385.84939452907383,
				382.6040872683748,
				383.2032550186086,
				387.1947011566472,
				391.52701886043286,
				392.4703254092283,
				395.3039262721888,
				397.0317449675258,
				395.0752206666483,
				390.2456733944047,
				387.7268913593091,
				388.81128427597514,
				386.56173784076213,
				382.5302123009875,
				380.22844905480713,
				376.4815564950119,
				375.10059888235475,
				372.1918094690118,
				371.272792555953,
				369.4874399558467,
				366.186652492791,
				366.0232521283291,
				368.9635262170986,
				363.45566629243905,
				361.6568939178612,
				360.7814171503468,
				358.67926877954636,
				354.4010071725235,
				353.10495406776766,
				353.76489695146444,
				348.84239887719093,
				348.8119877109467,
				345.06497691461493,
				340.99688536737546,
				325.0494167551213,
				319.08485585877634,
				318.99236499950706,
				322.5705349375218,
				324.1828086236344,
				329.76006675772794,
				330.64366285880806,
				331.8088224241234,
				333.93914258930283,
				334.3090193567946,
				333.4704281541281,
				333.68882554825757,
				339.60362481477716,
				344.6143186633552,
				345.65790432291925,
				351.0183945449126,
				353.6826327751169,
				351.6397807976003,
				349.050587160486,
				346.1240806295677,
				339.4771905686904,
				335.7298240644194,
				330.19882946969096,
				321.544243028629,
				311.7865800422883,
				305.6352149412978,
				305.22934625465854,
				305.86555452747695,
				308.5786164902202,
				311.61518643906334,
				308.71314507103097,
				305.8777628823138,
				305.2752857572404,
				304.40036526236327,
				305.04069620652723,
				303.46223944781303,
				306.3041151565866,
				311.7696151643108,
				318.3243771398669,
				319.23556316478266,
				319.11052081305394,
				318.62823330788257,
				321.9094007298669,
				326.28352680239277,
				332.7846220775087,
				337.09007833603573,
				338.83680363627104,
				342.9298159573705,
				345.8388266021207,
				347.39064778730483,
				345.77593004006417,
				345.166271969549,
				348.03767790769274,
				350.06807158362807,
				352.66217512006034,
				354.2297523293912,
				349.23532908612117,
				347.44639553117355,
				345.9178228577388,
				344.8056822305583,
				345.14076445362537,
				345.23030662406575,
				346.4161299528388,
				346.32040857717,
				349.9646296163927,
				353.8765763407605,
				358.1556518845615,
				360.8916995305604,
				362.5867487033055,
				362.4206686248174,
				359.81943897430097,
				353.9868533887703,
				348.21463424201653,
				339.9894949146616,
				334.9846795426847,
				334.09152213479763,
				331.89778855511145,
				329.46184855516213,
				321.5902885667174,
				319.91621859112723,
				319.41947259797314,
				321.0862549780305,
				323.56196185526494,
				326.6794556502465,
				329.6052999944971,
				331.73158901832744,
				330.6224684780846,
				330.09894253008093,
				326.29893873925175,
				315.10460322097595,
				312.217865284604,
				307.5235362590053,
				304.5910387844308,
				303.2260357362862,
				300.22660426959055,
				298.47247861671764,
				297.9999809651936,
				296.61039578568176,
				298.06841670420107,
				297.6754460131242,
				297.2788449059776,
				295.00136658423384,
				294.8150354230192,
				295.59168740522585,
				296.8932521330827,
				297.99112496687246,
				296.3390101943839,
				294.3290193657384,
				297.1768648567962,
				298.0953545922771,
				297.0487398430146,
				296.07633669287225,
				298.4183031478399,
				297.05535879637875,
				297.26455727930767,
				297.31795967460937,
				298.43898117052015,
				301.0076466794756,
				305.35360031991263,
				307.5343910116981,
				309.7795729717523,
				311.71302681747954,
				313.55163073495993,
				314.4578953482569,
				315.0854584731018,
				316.82820189767676,
				317.8561667890849,
				315.1527743241306,
				312.06130928698497,
				309.10804699113567,
				308.76099225676927,
				300.004761682398,
				301.352624467631,
				301.3127353241994,
				301.8758266539654,
				302.0891089275469,
				302.12019284163847,
				302.3509827629092,
				302.372812139653,
				302.4904093717308,
				302.52439929956023,
				303.85551270367773,
				305.9280869185618,
				309.2417626111275,
				311.74557043491933,
				309.64125810266603,
				308.34221074186524,
				305.8632563903434,
				305.8413879746014,
				307.9794344194539,
				307.63492235717024,
				305.8246316462689,
				304.37049722926395,
				300.3901945998764,
				293.3943221360708,
				284.42465583610016,
				281.7984585914306,
				281.4166755862907,
				285.1173121708155,
				289.02257939671443,
				292.5777518124788,
				296.5325106396011,
				298.2356967731126,
				298.3376803962144,
				299.28965368374645,
				304.2794028269798,
				307.4906149730372,
				308.72059354314194,
				311.7591241572703,
				312.50537525122775,
				311.9713085425563,
				312.6069288480361,
				315.96324690702426,
				325.8980017333381,
				329.99061640801983,
				332.14448368343733,
				336.0690124232207,
				335.3903297999555,
				331.0318295432315,
				328.37447702690497,
				328.13215041415214,
				327.55425160959624,
				326.28863795987894,
				325.06663976989205,
				324.380108292613,
				322.46286078251404,
				322.6187138538855,
				323.298011724096,
				322.66835686479436,
				322.6579717865607,
				322.9735949222176,
				320.9995519908485,
				320.1458954422267,
				319.78371555657236,
				319.83002912052376,
				318.30405775822646,
				315.38244267965507,
				309.6969729420101,
				308.2634675954324,
				306.5165142091233,
				307.0864973759073,
				308.09839513655777,
				313.1829037033539,
				316.05334480429167,
				318.9871051177584,
				323.4801620933797,
				324.48145402215323,
				326.8849595327132,
				329.71062431016304,
				331.32235174132984,
				331.92845955558244,
				334.19375293097926,
				335.68278109924944,
				336.24334933541604,
				334.99078167252196,
				334.823876608745,
				332.82476836740483,
				333.69958704031006,
				333.5805576187981,
				332.77647003521434,
				332.5940618126947,
				331.95793915802807,
				331.90674333474914,
				331.91689182815327,
				330.67318802180426,
				326.91400446268807,
				325.75657928373283,
				325.60583309640396,
			],
			yaxis: "y",
			hoverlabel: {
				namelength: 10,
			},
		},
		{
			connectgaps: true,
			hovertemplate: "%{y}<extra></extra>",
			line: {
				color: "#e4003a",
				width: 1,
			},
			mode: "lines",
			name: "BBL_15_4.0         ",
			opacity: 1,
			type: "scatter",
			x: [
				"2020-04-20T00:00:00",
				"2020-04-21T00:00:00",
				"2020-04-22T00:00:00",
				"2020-04-23T00:00:00",
				"2020-04-24T00:00:00",
				"2020-04-27T00:00:00",
				"2020-04-28T00:00:00",
				"2020-04-29T00:00:00",
				"2020-04-30T00:00:00",
				"2020-05-01T00:00:00",
				"2020-05-04T00:00:00",
				"2020-05-05T00:00:00",
				"2020-05-06T00:00:00",
				"2020-05-07T00:00:00",
				"2020-05-08T00:00:00",
				"2020-05-11T00:00:00",
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
			],
			xaxis: "x",
			y: [
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				192.3513604400425,
				190.42376318509724,
				194.37895211979168,
				196.07888819093208,
				198.2475226326075,
				199.12584649141937,
				198.62440122893744,
				200.96460768946923,
				199.14132826567752,
				198.94753721948683,
				201.8802052399952,
				204.50780105160027,
				206.2494648870548,
				208.02098398196685,
				208.00061418505135,
				207.31645041517552,
				206.1502026228524,
				206.52702418559355,
				209.8719914866436,
				211.111945699749,
				211.87390170144133,
				210.8920307265334,
				209.95539737136065,
				210.43534301535772,
				211.97616349490937,
				213.41347103539417,
				214.89454354606752,
				216.26011265889105,
				218.3870990272731,
				219.48312344058095,
				219.9641157579414,
				219.70499847671724,
				220.78452859027863,
				222.7024172793346,
				222.92679703655512,
				223.09246682212225,
				222.62404778715435,
				221.47733394790623,
				223.6467619355524,
				222.8230310567693,
				223.1127216473888,
				221.33906382699703,
				219.59368149280212,
				218.1360656132718,
				219.21893180886752,
				219.5266642083836,
				219.6178003484083,
				222.0956790190537,
				224.1356025948421,
				228.9604113424383,
				234.88050857855117,
				238.9601332422119,
				242.4080912067697,
				244.38893067316636,
				244.70788698037688,
				244.79023015387412,
				244.76001935066176,
				244.7816822348062,
				244.252598895164,
				242.62557160707732,
				240.9709433735192,
				239.6147548744925,
				237.8631966367408,
				237.9869566125576,
				237.61719039281513,
				237.7130602726664,
				237.2004650454367,
				238.5682500859926,
				242.00927627923124,
				243.2208276890683,
				246.596467273435,
				250.04933511855097,
				252.62404899241616,
				252.61320599694045,
				251.3025738447869,
				249.6783390333783,
				246.086212414947,
				243.78806696359905,
				242.71846359593889,
				242.36991807327223,
				244.08173867853804,
				243.3559680407798,
				246.79785224723943,
				250.62810104986733,
				247.900308308868,
				247.8708246467889,
				246.26115508928868,
				242.77101368509216,
				241.091303384248,
				240.34352390259696,
				238.35974294642844,
				235.6780432162885,
				232.3559380097587,
				230.5236919093643,
				231.6771107581627,
				234.19270871740616,
				243.7329812063599,
				247.64492203597345,
				250.22070558531232,
				250.2280401314289,
				250.2390142630836,
				248.23856301832737,
				248.5845011912624,
				247.787892025634,
				248.28854410943177,
				247.58594809308727,
				247.23197784000956,
				247.2014358423899,
				243.99104445498588,
				241.3464769918907,
				244.506979166646,
				249.1854713724774,
				252.0060772701933,
				253.21773231737157,
				255.09388346914085,
				256.4562395083646,
				256.7156774593561,
				260.13921723168994,
				260.2398427256398,
				263.4740394268737,
				258.70579790581405,
				256.9276519232358,
				251.27135029109607,
				248.75382005726487,
				249.46256012001487,
				250.83524874038147,
				249.23116590513857,
				247.3712308196004,
				246.9397936793308,
				246.9464822214116,
				246.37317450729415,
				246.0949020580393,
				245.4704620486761,
				244.90004746517832,
				244.53858464783357,
				248.0042624191009,
				249.90342633824835,
				256.73186865359224,
				265.89139749711194,
				274.47008312955586,
				274.5982801969369,
				273.0011609713522,
				271.4928460361104,
				269.3274174920562,
				270.65147045383526,
				269.9231666618619,
				270.1141401737342,
				269.58573292363167,
				268.8082666411368,
				270.16374201630646,
				272.7314373231089,
				274.7556438439045,
				278.36714019546775,
				282.8032096701622,
				285.17069565687814,
				286.61154626159237,
				287.6727761127497,
				289.19132528565217,
				289.22674065980283,
				289.65614123842084,
				289.90541543687317,
				289.18885719591214,
				288.4449003244695,
				287.8395268503084,
				289.59079429658874,
				291.8727793724827,
				295.3388406801161,
				297.64288046988145,
				297.7737174144372,
				295.59967142505946,
				295.5567827017956,
				295.89894981947435,
				296.2106033640513,
				296.7378544055367,
				297.94104053300845,
				298.56242016871096,
				295.29998961623414,
				292.01950027521013,
				290.46169819279146,
				288.4370168845499,
				288.5591034692054,
				289.9373097957008,
				293.78050059652736,
				293.65532015176944,
				293.7093810108059,
				294.07493589982346,
				295.45238253002543,
				295.6161389121936,
				296.87031788074717,
				299.9753783780953,
				301.6681934551947,
				301.4493577174495,
				300.6386135090144,
				299.75496081555235,
				299.1333029947656,
				298.93041465853537,
				301.6603154987935,
				304.03940356223666,
				309.72042934222804,
				309.1433132168209,
				307.89915575668635,
				300.137042852009,
				294.77454892920633,
				294.00537482578886,
				292.1356443691898,
				286.6445381467259,
				279.81093451773114,
				277.3483344218824,
				272.67338023884645,
				274.1179762553262,
				275.9616270597234,
				279.153408585262,
				282.69780520226766,
				283.3539737991546,
				283.54172452233377,
				284.2067551950993,
				284.10885902753904,
				284.036546071605,
				285.212482093312,
				285.3159742820151,
				285.9018655739189,
				288.5084192412964,
				289.7193902662491,
				297.7937345308687,
				298.58013729877064,
				299.86345870667486,
				297.7914774220892,
				293.2366847916381,
				290.2610054631796,
				287.76843152845106,
				284.5458996154123,
				283.4911043549135,
				283.5880415686163,
				281.70981361066345,
				282.30143171301256,
				284.59027862413586,
				288.69147385985474,
				292.5103330691539,
				297.88674318753544,
				306.1949431379999,
				313.65614926418846,
				318.83432675631894,
				320.20967276563715,
				322.69243152496495,
				325.520541710849,
				326.58235465215,
				326.8047935529865,
				326.61374736949637,
				322.344134146803,
				318.76268928339556,
				317.4649345421403,
				317.60775931073556,
				313.5410245152936,
				310.12320562043016,
				302.6009369790553,
				298.24360146466927,
				297.7880271484381,
				297.89309180702554,
				297.60988993658106,
				297.86386474986125,
				300.369945022629,
				302.55627242996906,
				303.86608285857676,
				303.2655846840847,
				302.3206345695395,
				302.0529565820213,
				302.1299317239055,
				301.88660230635276,
				301.7886699678846,
				305.9882337656479,
				308.9209145691598,
				308.8761375742709,
				310.04959590652516,
				313.30918497231755,
				316.5702459486413,
				316.4497714608832,
				316.70962526096434,
				316.4077812458317,
				316.70938116849305,
				315.69123852522296,
				316.4158710139126,
				316.649836098813,
				316.8422250394149,
				317.5188697816184,
				320.5377964839948,
				321.5027318162516,
				321.1214139821512,
				321.30422317461455,
				322.8745616462945,
				323.5854954669011,
				323.21103332379363,
				322.04075239998673,
				322.40084125024526,
				325.07663903151416,
				325.9366344366037,
				328.739613102646,
				331.5324271725316,
				333.3522145951938,
				336.45802311343493,
				339.0465505108462,
				341.8511616568054,
				343.2565420953363,
				344.18088083278735,
				345.20435369765096,
				344.86169331542277,
				343.61013203347596,
				343.6800054235955,
				343.4905077448135,
				343.85991698483855,
				344.08399723595625,
				344.1397919131054,
				343.9853019930528,
				343.7616778077154,
				343.6535417804257,
				345.525270476412,
				350.5067007759127,
				353.84694838569965,
				355.84461311127274,
				356.6969734934413,
				356.57377740483656,
				356.46438546408694,
				356.97780267660795,
				355.1081344092018,
				354.4299691137941,
				354.89586672069635,
				353.6718837301657,
				352.05141375410017,
				350.7851481044453,
				350.44773171600616,
				349.22372723327925,
				346.5925474866104,
				345.417581122773,
				344.93161611746564,
				344.7599519927239,
				344.42956605925883,
				344.415578057893,
				346.4048560157753,
				351.5782119110804,
				357.19757203616865,
				361.321035209415,
				362.1141670212613,
				363.0228898169962,
				363.8923163768187,
				364.7883040042691,
				358.17779187798976,
				353.8724675700883,
				352.27228207417403,
				352.1141979749221,
				352.1701031458267,
				352.09018435339635,
				347.43407254188463,
				343.7641928705425,
				340.3686214476793,
				338.45110600719454,
				333.5314048757531,
				331.92471687027427,
				332.6103386932286,
				334.9844224925482,
				336.05974728633606,
				335.25844945119854,
				334.33255468481445,
				334.9373307806292,
				337.11189409190155,
				339.2551064783387,
				338.0396249188818,
				334.42324902074176,
				332.50466868527997,
				330.9998307074042,
				330.8055699807667,
				333.2237997968375,
				334.2478224404184,
				335.0847600019691,
				334.3354557863946,
				334.7423634993452,
				337.4827938721302,
				342.46575159249835,
				346.33375203259106,
				345.97599783687144,
				346.45175913517573,
				347.1062073258448,
				348.52807865114244,
				351.5627044111479,
				354.11922072723,
				358.8025350721907,
				362.476712504176,
				366.1205778840627,
				370.5641119979538,
				372.302553626231,
				373.1623343991199,
				375.64106300972,
				377.9743815831145,
				378.7067426417227,
				377.197570194264,
				377.0844601452397,
				376.638752846726,
				373.627487360673,
				373.7954483388456,
				369.85335360296654,
				367.45717618910766,
				367.4813298979372,
				367.2949602284049,
				367.1486278506486,
				367.75686052557234,
				369.15407799167264,
				368.5619329328161,
				368.6243201321192,
				367.8961321051494,
				366.22002989746534,
				364.26435359723314,
				364.23324124537834,
				364.6608874268773,
				364.2741163999128,
				364.27943792404903,
				364.74587856017337,
				363.9914585745057,
				363.7709095957929,
				363.76649016816776,
				363.163168618818,
				363.5304248014792,
				362.225825323052,
				359.7685984927054,
				357.1297316200447,
				355.3925851206997,
				357.3077210770061,
				356.8968214994865,
				352.5002652810342,
				349.97859143319863,
				345.39226000194355,
				340.04361913161557,
				334.3002690788768,
				324.87302637060384,
				319.04449003742116,
				313.06786458252054,
				308.34295294099843,
				300.9968395248027,
				299.78168922180663,
				300.6512230580157,
				302.09409789762395,
				305.7681814323626,
				310.4696896953973,
				313.32107545868257,
				318.50967253509356,
				320.8115049588806,
				320.7404965018072,
				320.98472297236543,
				319.7434450602608,
				318.4710072016656,
				320.88693168376346,
				323.93446029575847,
				326.3736154876479,
				322.05990565545864,
				316.64559103373256,
				308.44520423427264,
				309.1242991565743,
				308.55512598664944,
				308.79164159226366,
				307.83430748155587,
				308.96923360008407,
				313.86394969239,
				314.88254375153707,
				307.46911635593347,
				301.1425592766697,
				303.00153094316744,
				305.30603604958975,
				302.19081503561796,
				296.28734481335454,
				295.1657667160698,
				295.8511709645176,
				295.29920134698625,
				293.62601654294775,
				292.623320199224,
				288.6636847789074,
				287.56514815081636,
				283.75632941994013,
				281.21800998982775,
				281.18004608935223,
				281.7612653077748,
				281.6351267076902,
				284.85036361716124,
				292.8298636882827,
				307.3952321607938,
				320.4940476244729,
				326.56759765842617,
				331.14735316131265,
				330.29599221446426,
				325.0885630035091,
				318.4393833205567,
				316.7709831845217,
				311.12961864968617,
				306.66813296216174,
				306.1569529987163,
				307.01964642981403,
				305.17991202610756,
				299.18127757298316,
				297.1609631683524,
				294.29085296593956,
				290.725693197797,
				291.1315904776444,
				287.10740893014525,
				285.95804811562374,
				283.9568379779012,
				284.4388702655074,
				283.3268932428861,
				279.62928693417086,
				271.1925772985264,
				270.6800229979255,
				265.9463165313575,
				261.13218148246574,
				260.13017946264114,
				260.2881000865911,
				260.13263951947187,
				256.08274302249384,
				255.92030864234027,
				252.69970255598037,
				253.1387950580413,
				253.0283180831454,
				263.90276423446204,
				268.05855641335904,
				268.13120677783667,
				266.02572157289484,
				265.10679098574064,
				262.5119547266471,
				263.6197241854628,
				263.1532015016579,
				262.5352592661659,
				262.3598852656013,
				264.2914126661844,
				263.9751026743987,
				256.30446437793114,
				248.96896584185313,
				247.33789645833073,
				239.3482761907645,
				233.42375964024765,
				231.83058459953511,
				230.83790567805568,
				231.0383664732969,
				235.14428973729923,
				237.14668553844515,
				239.6165391826528,
				244.89381035678764,
				251.1025801139617,
				255.04059072276473,
				255.27068629742476,
				256.47964810924185,
				256.3648934381652,
				255.0640168486971,
				260.3749612115211,
				264.8114013429467,
				266.0197297049992,
				267.6900725631575,
				267.98038940545194,
				268.868151990989,
				266.8018133915905,
				263.92011872240795,
				260.4913862064873,
				262.45830890552986,
				264.98818931064403,
				266.45283358664864,
				265.55927195893514,
				263.15117371844053,
				259.3160859303038,
				258.4879652837559,
				260.46515762674983,
				261.3090919202337,
				263.40198557235846,
				265.8113826488931,
				271.57689385316496,
				274.6762369822739,
				274.84071157798434,
				275.02425019371566,
				276.1951735127522,
				278.9745974427442,
				288.99478565997254,
				293.65786065372225,
				297.76759380892787,
				299.86075250251463,
				299.40503225861426,
				299.2850009931218,
				297.05452027502577,
				296.6438207522571,
				291.1025090554823,
				285.0296614522083,
				278.621492483928,
				272.3510983210021,
				267.3841985623195,
				262.88112580877635,
				260.1781928616365,
				261.5948360643547,
				263.6590392605877,
				268.94242647205715,
				272.34048728674236,
				270.4947286790044,
				270.2929015490553,
				269.5590661583795,
				273.28645703875134,
				272.8570276653832,
				271.34384527312056,
				267.42993154540693,
				262.4755829038496,
				256.256603920066,
				250.63864531800297,
				246.11590447125593,
				244.7688930128008,
				241.55903273033576,
				240.1424024065816,
				246.46893600428447,
				247.88903982607312,
				250.78793021234884,
				252.2837211439546,
				251.0322487689221,
				250.75926405723231,
				249.06300722312616,
				246.75979279782723,
				246.65527397343277,
				243.2356604442365,
				242.9619278475528,
				242.96009959272033,
				243.98253234154737,
				243.80230669937663,
				244.10937867550336,
				243.46123598540692,
				242.63960826880464,
				243.75384055431405,
				244.84877604441783,
				243.61057817705793,
				244.2247096980875,
				246.91284462964165,
				248.28633013655485,
				244.83806078445187,
				246.72427580648585,
				246.28132895116113,
				245.97028088528648,
				243.89701736463613,
				243.13987529318064,
				240.45935134024364,
				239.20689723699985,
				237.6496669371018,
				236.71361299371833,
				236.46075126373805,
				236.03260351242025,
				235.92830698913775,
				235.62522014008368,
				237.6704608150818,
				243.88400546102565,
				249.37002209322335,
				254.04715320417688,
				257.557721122137,
				270.3513499712478,
				270.305013064921,
				270.302405463561,
				269.23994889942,
				267.8499739175052,
				267.60772463883023,
				267.00905548578874,
				267.08815791243035,
				267.8683512076963,
				267.9087305832523,
				264.99320555804104,
				261.237936844459,
				256.28882007116414,
				252.405829304664,
				251.95285810827147,
				249.75619013704096,
				248.974687131792,
				245.88496700586737,
				240.96359943471268,
				239.52154736939227,
				239.08304250737697,
				238.28913818740276,
				239.71899729465488,
				243.20289059179373,
				249.9059675362957,
				251.77484463122568,
				252.0547843746468,
				249.39253890340325,
				246.75330764755637,
				244.1992501406462,
				242.2121101286281,
				241.90942448339777,
				243.347724063421,
				245.40176070427435,
				243.41003402197853,
				243.13960149831695,
				245.00482963394143,
				245.64061135705265,
				249.51121410424108,
				252.90352462801664,
				255.4736050061306,
				255.85683365938195,
				250.47103716640152,
				249.99016565578225,
				250.8476346433856,
				250.69592898302932,
				254.90115376124237,
				262.7915184385393,
				267.7146994053867,
				270.0258248462646,
				273.05060271983086,
				277.16114638256903,
				279.7090926519829,
				281.04794753421,
				283.465752498736,
				283.2606440562708,
				282.03552505975813,
				280.0199447302577,
				278.4008538970331,
				276.74996464809493,
				276.23719524222446,
				275.6584014327733,
				275.7543256218131,
				275.7506105279137,
				276.1902619032319,
				277.9230504844074,
				281.41990938220863,
				280.88978761290093,
				281.24289741197043,
				281.11755942747817,
				280.7286963347964,
				276.9741275466462,
				275.90222973997913,
				274.6330486908353,
				272.4378270016724,
				273.4858799622218,
				273.29488665869303,
				272.0256549867119,
				271.6410963380972,
				272.5333975407717,
				272.31615104037496,
				273.9567696820006,
				277.77300401744856,
				283.01348265039474,
				286.135010110005,
				290.4610633383243,
				291.1481424518774,
				292.99810855958737,
				295.1168608567128,
				295.65927070683654,
				298.0353958029095,
				299.2625926027509,
				300.3124416679405,
				302.9028088857478,
				308.2073252899161,
				309.8394168100172,
				310.11416202078357,
			],
			yaxis: "y",
			hoverlabel: {
				namelength: 10,
			},
		},
		{
			connectgaps: true,
			hovertemplate: "%{y}<extra></extra>",
			line: {
				color: "#ef7d00",
				dash: "dash",
				width: 1,
			},
			mode: "lines",
			name: "BBM_15_4.0         ",
			opacity: 1,
			type: "scatter",
			x: [
				"2020-04-20T00:00:00",
				"2020-04-21T00:00:00",
				"2020-04-22T00:00:00",
				"2020-04-23T00:00:00",
				"2020-04-24T00:00:00",
				"2020-04-27T00:00:00",
				"2020-04-28T00:00:00",
				"2020-04-29T00:00:00",
				"2020-04-30T00:00:00",
				"2020-05-01T00:00:00",
				"2020-05-04T00:00:00",
				"2020-05-05T00:00:00",
				"2020-05-06T00:00:00",
				"2020-05-07T00:00:00",
				"2020-05-08T00:00:00",
				"2020-05-11T00:00:00",
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
			],
			xaxis: "x",
			xhoverformat: "%Y-%m-%d",
			y: [
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				211.60150451660155,
				212.527397664388,
				213.65642496744792,
				214.2048848470052,
				214.94599609375,
				215.5639149983724,
				216.3417195638021,
				217.34821370442708,
				218.16009216308595,
				218.81273905436197,
				219.92145385742188,
				220.82703552246093,
				221.654638671875,
				222.37478332519532,
				223.13096516927084,
				223.7338124593099,
				224.30782979329427,
				225.26255798339844,
				226.2906768798828,
				227.4609873453776,
				228.65881754557293,
				229.69873046875,
				230.9673319498698,
				231.1396697998047,
				231.59901428222656,
				232.19465230305988,
				233.09957885742188,
				233.9730529785156,
				234.90942993164063,
				235.62236429850265,
				236.48136291503903,
				237.37532755533857,
				237.86309814453125,
				238.61192830403647,
				238.67403157552084,
				238.7862335205078,
				239.0962137858073,
				239.40455729166663,
				240.62391764322916,
				242.1283721923828,
				243.3279235839844,
				244.48068339029948,
				245.72673848470052,
				247.0470438639323,
				248.01353861490887,
				248.9082234700521,
				249.6998555501302,
				250.711382039388,
				251.59031473795577,
				253.3337371826172,
				254.7227010091146,
				255.8602559407552,
				256.3538787841797,
				256.57509053548176,
				256.6886484781901,
				256.7017801920573,
				256.6873372395833,
				256.6184122721354,
				256.7378784179688,
				257.4494252522787,
				258.1064910888672,
				258.7727406819661,
				259.79148763020834,
				260.58246053059895,
				260.8141723632813,
				260.8922831217448,
				261.3471720377604,
				262.2969899495443,
				263.3866282145182,
				264.384369913737,
				265.77332865397136,
				266.8452473958333,
				268.0792989095052,
				269.1348022460937,
				270.0701883951823,
				271.0863077799479,
				272.4588582356771,
				273.53602091471356,
				274.9184204101563,
				276.5312194824219,
				278.81092529296876,
				280.8392333984375,
				281.8192545572917,
				282.5688741048177,
				282.2163859049479,
				282.2104797363281,
				281.9636698404948,
				281.3413940429688,
				280.90094604492185,
				280.60096232096356,
				279.8625020345052,
				278.44203898111977,
				276.85484619140624,
				275.21250813802084,
				273.7421468098958,
				271.39483032226565,
				268.94182942708335,
				267.9014180501302,
				267.4826293945313,
				267.87647298177086,
				267.87384643554685,
				268.52434895833335,
				268.77772216796876,
				269.1059244791667,
				268.8499267578125,
				269.1984822591146,
				269.9264383951823,
				271.1631123860677,
				272.936728922526,
				274.3834493001302,
				276.21482950846354,
				277.83681437174477,
				278.9494303385417,
				279.382655843099,
				279.93797810872394,
				280.35479736328125,
				280.4814798990885,
				281.16611531575523,
				281.18646443684895,
				281.67876790364585,
				281.1352600097656,
				280.8057454427083,
				279.73382771809895,
				278.1223449707031,
				276.81806640625,
				276.4800170898437,
				276.76161702473956,
				277.1620259602865,
				277.4764465332031,
				277.4114624023438,
				277.77708129882814,
				278.0540832519531,
				278.4604024251302,
				279.2960144042969,
				279.92682495117185,
				281.1392110188802,
				282.1888061523438,
				283.5672648111979,
				284.90699666341146,
				286.20799560546874,
				286.8230509440104,
				287.1263122558594,
				287.4551737467448,
				288.43125610351564,
				289.7723002115885,
				290.72540690104165,
				291.84918212890625,
				292.9204406738281,
				293.91095581054685,
				294.5043518066406,
				295.31895955403644,
				295.9405782063802,
				296.8372314453125,
				297.9465616861979,
				298.899013264974,
				299.86524861653646,
				300.5905802408854,
				301.2377950032552,
				301.6881856282552,
				302.01039225260416,
				302.39380900065106,
				302.9009989420573,
				303.31304321289065,
				303.65748087565106,
				304.51072591145834,
				304.99322509765625,
				305.68739217122396,
				305.95372721354164,
				306.49598185221356,
				307.1936340332031,
				307.4555318196615,
				307.7462565104167,
				308.21446533203124,
				308.5163065592448,
				308.756982421875,
				309.2074340820312,
				309.93671264648435,
				310.8178955078125,
				311.63661092122396,
				312.5815795898437,
				313.84943237304685,
				314.3472391764323,
				315.2534118652344,
				315.225791422526,
				315.44871826171874,
				316.32069295247396,
				317.1400573730469,
				318.0738464355469,
				319.1924194335937,
				320.6207255045573,
				321.74521891276044,
				322.3357442220052,
				322.8762898763021,
				323.60161743164065,
				324.0902140299479,
				324.441367594401,
				325.2988749186198,
				325.9367411295573,
				326.4549275716146,
				326.3931131998698,
				326.1596659342448,
				325.26862182617185,
				324.2098917643229,
				323.698935953776,
				322.70070190429686,
				321.0994506835938,
				319.2154276529948,
				317.5122477213542,
				315.1113566080729,
				313.54824015299477,
				312.0324768066406,
				311.08356119791665,
				310.0603393554687,
				309.8229451497396,
				309.763759358724,
				309.6171162923177,
				309.56516723632814,
				309.49940388997396,
				309.19838460286456,
				309.1471374511719,
				309.3512491861979,
				309.85254720052086,
				310.3601521809896,
				311.4365763346354,
				311.6303690592448,
				312.19939575195315,
				312.6557657877604,
				313.7084594726563,
				314.52439778645834,
				315.2779256184896,
				316.17246704101564,
				317.84906209309895,
				319.4243570963542,
				320.8722452799479,
				322.14235229492186,
				324.10052083333335,
				326.12058512369794,
				327.6270731608073,
				328.9768534342448,
				330.62095336914064,
				331.6790466308594,
				332.6568155924479,
				333.3534322102865,
				333.9677449544271,
				334.45367024739585,
				334.79210611979164,
				334.8500528971354,
				334.8184427897135,
				334.1297220865885,
				333.63721720377606,
				332.9722045898437,
				332.4579711914063,
				331.5941121419271,
				330.8632527669271,
				329.38837076822915,
				328.34410196940104,
				327.4861673990885,
				326.35235392252605,
				325.1717956542969,
				324.0913106282552,
				323.341357421875,
				322.61840006510414,
				322.3761006673177,
				322.56243693033855,
				322.8982381184896,
				322.9884419759115,
				322.9706624348958,
				323.4355122884115,
				323.97278849283856,
				324.8359883626302,
				325.90659790039064,
				326.5788533528646,
				327.3920125325521,
				328.35463256835936,
				329.5240030924479,
				330.3411153157552,
				331.4933675130208,
				332.1353373209635,
				332.6647135416667,
				333.40280965169273,
				334.04477945963544,
				334.75782470703126,
				335.75555419921875,
				336.72169392903646,
				338.0580362955729,
				338.99730631510414,
				340.15089314778646,
				341.3783223470052,
				342.5635660807292,
				343.5269348144531,
				344.6999959309896,
				345.7599182128906,
				347.0180908203125,
				348.21486409505206,
				349.2741943359375,
				350.6049051920573,
				351.7954081217448,
				352.81715698242186,
				353.65960489908855,
				354.1678426106771,
				354.51128540039065,
				354.8461588541667,
				355.2792500813802,
				355.9068074544271,
				356.80528767903644,
				357.45458984375,
				357.7340881347656,
				358.05577189127604,
				358.5640096028646,
				358.798681640625,
				358.9423868815104,
				359.2317728678385,
				359.51324869791665,
				360.1164103190104,
				360.8052652994792,
				361.7320882161458,
				362.262733968099,
				362.5692565917969,
				362.80524291992185,
				362.8461140950521,
				362.87841796875,
				362.9641153971354,
				362.7254903157552,
				362.5593709309896,
				362.7676778157552,
				363.33656005859376,
				363.8349100748698,
				364.32666829427086,
				364.50794881184896,
				365.0346435546875,
				365.79469197591146,
				366.658896891276,
				367.6061543782552,
				368.4545389811198,
				369.2950052897135,
				370.1605244954427,
				371.15260823567706,
				372.2903727213542,
				373.1229349772136,
				373.6898376464843,
				373.8236551920573,
				374.0662353515625,
				374.2976094563802,
				374.3905537923177,
				373.7356750488281,
				372.832314046224,
				372.1750813802083,
				371.7013590494791,
				371.2625935872395,
				370.550048828125,
				369.110302734375,
				367.71929728190105,
				366.3275370279948,
				365.2721232096354,
				363.734950764974,
				362.582432047526,
				361.3971211751302,
				360.412451171875,
				359.60365193684896,
				359.121250406901,
				358.52666015625,
				357.8951110839844,
				357.47605997721354,
				357.1863525390625,
				357.338134765625,
				358.36761678059895,
				359.40369873046876,
				360.6780090332031,
				361.5966267903646,
				363.26689453125,
				364.7015665690104,
				366.04319458007814,
				367.4455281575521,
				369.0920349121094,
				371.0084493001302,
				373.1142639160156,
				375.3038920084635,
				377.3885904947917,
				379.34592488606773,
				381.0227905273438,
				382.3327392578125,
				383.2916076660157,
				384.1706258138021,
				385.5300659179687,
				386.6308186848958,
				387.8384806315104,
				389.0019287109375,
				390.1587748209635,
				391.3387166341146,
				392.12204793294273,
				392.67836303710936,
				393.0466023763021,
				392.5833353678385,
				392.6552673339844,
				392.3754597981771,
				391.8343180338542,
				391.8607137044271,
				391.3684122721354,
				390.80747680664064,
				391.019970703125,
				391.16449178059895,
				390.907118733724,
				390.6603088378906,
				389.88622029622394,
				389.1537048339843,
				389.1253275553385,
				388.3386962890625,
				387.92360229492186,
				386.7018351236979,
				386.4304138183594,
				386.9143493652344,
				387.41041056315106,
				388.78736572265626,
				389.83815714518227,
				390.1174926757813,
				390.2000712076823,
				390.5054951985677,
				390.7815450032552,
				391.09278767903646,
				390.8671508789063,
				390.039638264974,
				389.6110290527343,
				389.32630411783856,
				389.66527506510414,
				389.5423746744792,
				388.4659891764323,
				387.3486307779948,
				385.1727233886719,
				382.8514526367187,
				380.2196207682291,
				377.00433146158855,
				374.06061197916665,
				370.3226542154948,
				366.8932779947917,
				364.0394246419271,
				361.9104370117187,
				360.7996846516927,
				359.83562622070315,
				358.6905192057292,
				356.4571268717448,
				355.1587178548177,
				353.5147338867188,
				352.7594807942708,
				352.7760030110677,
				352.55861206054686,
				352.2526794433594,
				351.8687723795573,
				352.59693603515626,
				353.354833984375,
				353.64226888020835,
				352.96234130859375,
				351.3124084472656,
				348.927040608724,
				347.07689208984374,
				346.5628153483073,
				345.8220947265625,
				344.9208089192708,
				344.1344930013021,
				342.51759643554686,
				341.12139689127605,
				339.6544921875,
				338.0594034830729,
				336.6625447591146,
				335.0258280436198,
				333.634912109375,
				332.0926818847656,
				331.43654174804686,
				332.156776936849,
				332.41778361002605,
				332.795741780599,
				333.04107055664065,
				334.0924357096354,
				334.42579142252606,
				335.6042439778646,
				337.09059041341146,
				339.7667704264323,
				342.9582010904948,
				345.1092488606771,
				347.2050801595052,
				349.70921223958334,
				353.1178792317708,
				355.32460530598956,
				356.20849609375,
				356.87572021484374,
				356.74962361653644,
				356.1416320800781,
				354.9832010904948,
				354.620654296875,
				353.2167724609375,
				351.8499389648438,
				350.6160868326823,
				348.6326599121094,
				346.4534016927083,
				343.99628092447915,
				341.86135050455727,
				338.41053263346356,
				335.47707112630206,
				333.80657348632815,
				331.10400390625,
				329.07492879231773,
				327.6148152669271,
				326.96315511067706,
				324.75677286783855,
				322.82626953125,
				320.0780517578125,
				317.0678446451823,
				313.8016052246094,
				310.95679931640626,
				309.40472412109375,
				307.3445536295573,
				306.61879679361977,
				304.92381998697914,
				302.3813537597656,
				300.75584513346354,
				299.1018859863281,
				297.0126017252604,
				294.47609049479166,
				293.5717061360677,
				293.56178588867186,
				294.2981282552083,
				294.6447998046875,
				296.1360107421875,
				297.13169352213544,
				297.48101196289065,
				298.23720092773436,
				298.33445231119794,
				298.88092041015625,
				298.83196411132815,
				297.95404459635415,
				296.79164225260416,
				296.497900390625,
				295.18333536783854,
				293.5531962076823,
				291.7351826985677,
				289.9442464192708,
				288.5812235514323,
				287.3107401529948,
				286.43825480143227,
				284.9076843261719,
				283.2190266927083,
				281.444580078125,
				280.33790283203126,
				280.25001627604166,
				281.1726013183594,
				282.4717549641927,
				283.3396016438802,
				284.54405314127604,
				285.34458211263023,
				285.6475077311198,
				286.0452189127604,
				286.5105428059896,
				286.165195719401,
				286.55296427408854,
				287.84486694335936,
				289.4078816731771,
				290.84693603515626,
				292.049355061849,
				292.5405334472656,
				293.734336344401,
				294.71735026041665,
				296.05035400390625,
				297.7890218098958,
				299.65098063151044,
				302.1194539388021,
				304.6204060872396,
				306.60101521809895,
				308.67641194661456,
				309.92125447591144,
				311.43919474283854,
				312.54616088867186,
				314.4286743164063,
				316.6021748860677,
				319.11505737304685,
				320.5521280924479,
				321.84270833333335,
				322.3332173665365,
				322.2728983561198,
				322.2576538085938,
				321.7353251139323,
				321.48211466471355,
				320.5335693359375,
				319.4531188964844,
				318.38857218424477,
				316.62139892578125,
				314.9854736328125,
				312.65089721679686,
				309.9988159179687,
				307.7908447265625,
				305.9368367513021,
				304.46596069335936,
				303.66258341471354,
				302.293125406901,
				301.09534505208336,
				299.5104573567708,
				297.43837280273436,
				296.3866231282552,
				295.38165893554685,
				294.2580932617187,
				293.0187723795573,
				291.46802978515626,
				290.12197265625,
				288.9237467447917,
				287.6956807454427,
				285.82898763020836,
				283.2206705729167,
				280.7867696126302,
				280.05345255533854,
				279.1557332356771,
				278.4373799641927,
				277.12914225260414,
				275.49293416341146,
				273.7677429199219,
				272.3798868815104,
				271.63283487955727,
				270.65203857421875,
				270.3186869303385,
				270.119472249349,
				269.4919494628906,
				269.3086710611979,
				269.8505330403646,
				270.1772440592448,
				270.31536661783855,
				270.046425374349,
				269.5888977050781,
				270.3937215169271,
				271.1600321451823,
				271.9807922363281,
				272.18133341471355,
				271.62818196614586,
				271.8898173014323,
				271.7729431152344,
				271.6441202799479,
				271.1679992675781,
				272.0737609863281,
				272.90647583007814,
				273.370644124349,
				273.71461995442706,
				274.21331990559895,
				275.006190999349,
				275.24524943033856,
				275.5068827311198,
				276.2267110188802,
				277.76331380208336,
				279.5183898925781,
				280.7156656901042,
				281.5776000976563,
				283.15935668945315,
				285.1780558268229,
				285.828818766276,
				285.8075703938802,
				285.5578877766927,
				284.96954142252605,
				284.86395874023435,
				284.680019124349,
				284.73048502604166,
				285.17938028971355,
				285.21656494140626,
				284.4243591308594,
				283.5830118815104,
				282.7652913411458,
				282.07569986979166,
				280.79705810546875,
				279.0492004394531,
				277.4189717610677,
				275.86317749023436,
				274.4715169270833,
				273.57823486328124,
				272.45383707682294,
				271.32981770833334,
				270.0545959472656,
				268.29860636393227,
				267.1653116861979,
				266.7866516113281,
				266.73572998046876,
				267.2549255371094,
				267.8879435221354,
				268.3885009765625,
				269.3723103841146,
				270.0725606282552,
				270.8427022298177,
				272.3457071940104,
				273.84471842447914,
				275.3151082356771,
				276.8627115885417,
				278.6998677571615,
				281.0082946777344,
				282.4374165852865,
				284.04026692708334,
				285.9100402832031,
				288.1845194498698,
				289.990391031901,
				291.4960591634115,
				293.382470703125,
				295.14574178059894,
				296.9116739908854,
				298.0445882161458,
				299.07898763020836,
				300.30242716471355,
				301.724892171224,
				302.3878662109375,
				302.7140279134115,
				302.964306640625,
				302.93967895507814,
				302.66676839192706,
				301.344150797526,
				300.5294128417969,
				299.8617797851563,
				298.6183736165365,
				297.9021484375,
				297.76902058919273,
				297.7903198242187,
				297.2471598307292,
				296.6527465820312,
				295.5584411621094,
				294.5766276041667,
				293.87970581054685,
				294.10202840169273,
				294.4135457356771,
				295.078515625,
				295.9777872721354,
				296.81007690429686,
				297.95899454752606,
				298.9836669921875,
				300.0899230957031,
				300.8681396484375,
				301.4817240397135,
				302.2309285481771,
				303.2549519856771,
				304.819775390625,
				307.0081766764323,
				309.00213216145835,
				310.479443359375,
				311.6429158528646,
				312.4238647460937,
				313.2893330891927,
				313.94666544596356,
				314.1266662597656,
				314.9966674804688,
				315.58466796875,
				316.1146667480469,
				316.787998453776,
				317.5606648763021,
				317.797998046875,
				317.85999755859376,
			],
			yaxis: "y",
			hoverlabel: {
				namelength: 10,
			},
		},
	],
	layout: {
		annotations: [
			{
				font: {
					color: "#ef7d00",
					size: 14,
				},
				opacity: 0.9,
				text: "",
				x: 0,
				xanchor: "left",
				xref: "paper",
				xshift: -60,
				y: 0.98,
				yref: "paper",
				yshift: 0,
			},
			{
				font: {
					color: "gray",
					size: 24,
				},
				opacity: 0.5,
				text: "",
				textangle: -90,
				x: 0,
				xanchor: "left",
				xref: "paper",
				xshift: -80,
				y: 0.5,
				yanchor: "middle",
				yref: "paper",
			},
		],
		hoverdistance: 2,
		margin: {
			autoexpand: true,
			b: 65,
			l: 70,
			pad: 0,
			r: 10,
			t: 40,
		},
		modebar: {
			activecolor: "#d1030d",
			bgcolor: "#2A2A2A",
			color: "#FFFFFF",
			orientation: "v",
		},
		newshape: {
			line: {
				color: "gold",
			},
		},
		showlegend: false,
		spikedistance: 2,
		template: {
			data: {
				bar: [
					{
						error_x: {
							color: "#f2f5fa",
						},
						error_y: {
							color: "#f2f5fa",
						},
						marker: {
							line: {
								color: "rgb(17,17,17)",
								width: 0.5,
							},
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "bar",
					},
				],
				barpolar: [
					{
						marker: {
							line: {
								color: "rgb(17,17,17)",
								width: 0.5,
							},
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "barpolar",
					},
				],
				candlestick: [
					{
						decreasing: {
							fillcolor: "#e4003a",
							line: {
								color: "#e4003a",
							},
						},
						increasing: {
							fillcolor: "#00ACFF",
							line: {
								color: "#00ACFF",
							},
						},
						type: "candlestick",
					},
				],
				carpet: [
					{
						aaxis: {
							endlinecolor: "#A2B1C6",
							gridcolor: "#506784",
							linecolor: "#506784",
							minorgridcolor: "#506784",
							startlinecolor: "#A2B1C6",
						},
						baxis: {
							endlinecolor: "#A2B1C6",
							gridcolor: "#506784",
							linecolor: "#506784",
							minorgridcolor: "#506784",
							startlinecolor: "#A2B1C6",
						},
						type: "carpet",
					},
				],
				choropleth: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "choropleth",
					},
				],
				contour: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "contour",
					},
				],
				contourcarpet: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "contourcarpet",
					},
				],
				heatmap: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "heatmap",
					},
				],
				heatmapgl: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "heatmapgl",
					},
				],
				histogram: [
					{
						marker: {
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "histogram",
					},
				],
				histogram2d: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "histogram2d",
					},
				],
				histogram2dcontour: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "histogram2dcontour",
					},
				],
				mesh3d: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "mesh3d",
					},
				],
				parcoords: [
					{
						line: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "parcoords",
					},
				],
				pie: [
					{
						automargin: true,
						type: "pie",
					},
				],
				scatter: [
					{
						marker: {
							line: {
								color: "#283442",
							},
						},
						type: "scatter",
					},
				],
				scatter3d: [
					{
						line: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatter3d",
					},
				],
				scattercarpet: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattercarpet",
					},
				],
				scattergeo: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattergeo",
					},
				],
				scattergl: [
					{
						marker: {
							line: {
								color: "#283442",
							},
						},
						type: "scattergl",
					},
				],
				scattermapbox: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattermapbox",
					},
				],
				scatterpolar: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterpolar",
					},
				],
				scatterpolargl: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterpolargl",
					},
				],
				scatterternary: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterternary",
					},
				],
				surface: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "surface",
					},
				],
				table: [
					{
						cells: {
							fill: {
								color: "#506784",
							},
							line: {
								color: "rgb(17,17,17)",
							},
						},
						header: {
							fill: {
								color: "#2a3f5f",
							},
							line: {
								color: "rgb(17,17,17)",
							},
						},
						type: "table",
					},
				],
			},
			layout: {
				annotationdefaults: {
					arrowcolor: "#f2f5fa",
					arrowhead: 0,
					arrowwidth: 1,
					showarrow: false,
				},
				autotypenumbers: "strict",
				coloraxis: {
					colorbar: {
						outlinewidth: 0,
						ticks: "",
					},
				},
				colorscale: {
					diverging: [
						[0, "#8e0152"],
						[0.1, "#c51b7d"],
						[0.2, "#de77ae"],
						[0.3, "#f1b6da"],
						[0.4, "#fde0ef"],
						[0.5, "#f7f7f7"],
						[0.6, "#e6f5d0"],
						[0.7, "#b8e186"],
						[0.8, "#7fbc41"],
						[0.9, "#4d9221"],
						[1, "#276419"],
					],
					sequential: [
						[0, "#0d0887"],
						[0.1111111111111111, "#46039f"],
						[0.2222222222222222, "#7201a8"],
						[0.3333333333333333, "#9c179e"],
						[0.4444444444444444, "#bd3786"],
						[0.5555555555555556, "#d8576b"],
						[0.6666666666666666, "#ed7953"],
						[0.7777777777777778, "#fb9f3a"],
						[0.8888888888888888, "#fdca26"],
						[1, "#f0f921"],
					],
					sequentialminus: [
						[0, "#0d0887"],
						[0.1111111111111111, "#46039f"],
						[0.2222222222222222, "#7201a8"],
						[0.3333333333333333, "#9c179e"],
						[0.4444444444444444, "#bd3786"],
						[0.5555555555555556, "#d8576b"],
						[0.6666666666666666, "#ed7953"],
						[0.7777777777777778, "#fb9f3a"],
						[0.8888888888888888, "#fdca26"],
						[1, "#f0f921"],
					],
				},
				colorway: [
					"#ffed00",
					"#ef7d00",
					"#e4003a",
					"#c13246",
					"#822661",
					"#48277c",
					"#005ca9",
					"#00aaff",
					"#9b30d9",
					"#af005f",
					"#5f00af",
					"#af87ff",
				],
				dragmode: "pan",
				font: {
					color: "#f2f5fa",
					family: "Fira Code",
					size: 18,
				},
				geo: {
					bgcolor: "rgb(17,17,17)",
					lakecolor: "rgb(17,17,17)",
					landcolor: "rgb(17,17,17)",
					showlakes: true,
					showland: true,
					subunitcolor: "#506784",
				},
				hoverlabel: {
					align: "left",
				},
				hovermode: "x",
				legend: {
					bgcolor: "rgba(0, 0, 0, 0)",
					font: {
						size: 15,
					},
					x: 0.01,
					xanchor: "left",
					y: 0.99,
					yanchor: "top",
				},
				mapbox: {
					style: "dark",
				},
				paper_bgcolor: "#000000",
				plot_bgcolor: "#000000",
				polar: {
					angularaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					bgcolor: "rgb(17,17,17)",
					radialaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
				},
				scene: {
					xaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
					yaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
					zaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
				},
				shapedefaults: {
					line: {
						color: "#f2f5fa",
					},
				},
				sliderdefaults: {
					bgcolor: "#C8D4E3",
					bordercolor: "rgb(17,17,17)",
					borderwidth: 1,
					tickwidth: 0,
				},
				ternary: {
					aaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					baxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					bgcolor: "rgb(17,17,17)",
					caxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
				},
				title: {
					x: 0.05,
				},
				updatemenudefaults: {
					bgcolor: "#506784",
					borderwidth: 0,
				},
				xaxis: {
					automargin: false,
					autorange: true,
					gridcolor: "#283442",
					linecolor: "#F5EFF3",
					mirror: true,
					rangeslider: {
						visible: false,
					},
					showgrid: true,
					showline: true,
					tick0: 1,
					tickfont: {
						size: 14,
					},
					ticks: "outside",
					title: {
						standoff: 20,
					},
					zeroline: false,
					zerolinecolor: "#283442",
					zerolinewidth: 2,
				},
				yaxis: {
					type: "log",
					anchor: "x",
					automargin: false,
					fixedrange: false,
					gridcolor: "#283442",
					linecolor: "#F5EFF3",
					mirror: true,
					showgrid: true,
					showline: true,
					side: "right",
					tick0: 0.5,
					ticks: "outside",
					title: {
						standoff: 20,
					},
					zeroline: false,
					zerolinecolor: "#283442",
					zerolinewidth: 2,
				},
			},
		},
		title: {
			text: "Mockup Data Title",
		},
		xaxis: {
			anchor: "y",
			domain: [0, 0.94],
			matches: "x3",
			rangebreaks: [
				{
					bounds: ["sat", "mon"],
				},
				{
					values: [
						"2020-12-25T00:00:00",
						"2022-05-30T00:00:00",
						"2023-01-02T00:00:00",
						"2022-12-26T00:00:00",
						"2021-01-18T00:00:00",
						"2020-09-07T00:00:00",
						"2020-07-03T00:00:00",
						"2022-06-20T00:00:00",
						"2020-11-26T00:00:00",
						"2020-05-25T00:00:00",
						"2021-07-05T00:00:00",
						"2021-02-15T00:00:00",
						"2023-02-20T00:00:00",
						"2022-04-15T00:00:00",
						"2022-11-24T00:00:00",
						"2021-01-01T00:00:00",
						"2022-09-05T00:00:00",
						"2021-04-02T00:00:00",
						"2023-04-07T00:00:00",
						"2021-11-25T00:00:00",
						"2022-01-17T00:00:00",
						"2023-01-16T00:00:00",
						"2021-09-06T00:00:00",
						"2021-05-31T00:00:00",
						"2022-07-04T00:00:00",
						"2021-12-24T00:00:00",
						"2022-02-21T00:00:00",
					],
				},
			],
			showticklabels: true,
			tickformatstops: [
				{
					dtickrange: [null, 604800000],
					value: "%Y-%m-%d",
				},
				{
					dtickrange: [604800000, "M1"],
					value: "%Y-%m-%d",
				},
				{
					dtickrange: ["M1", null],
					value: "%Y-%m-%d",
				},
			],
			type: "date",
			range: ["2020-04-20", "2023-04-21"],
			autorange: false,
			automargin: "t+b",
		},
		yaxis: {
			anchor: "x",
			domain: [0, 1],
			nticks: 15,
			tickfont: {
				size: 16,
			},
			type: "linear",
			range: [177.16199244245968, 442.3974072952108],
			autorange: true,
			automargin: "l+r",
		},
		yaxis2: {
			anchor: "x",
			overlaying: "y",
			side: "right",
			automargin: "l+r",
		},
		font: {
			family: "Fira Code, monospace, Arial Black",
			size: 18,
		},
		autosize: true,
		dragmode: "pan",
		xaxis3: {
			range: ["2020-04-20", "2023-04-21"],
			autorange: true,
			automargin: "t+b",
		},
		automargin: true,
		autoexpand: true,
	},
	port: 9999,
	python_version: "3.10.8",
	pywry_version: "0.5.0",
	terminal_version: "3.0.0",
	theme: "dark",
	user_id: "7dfe280a-4d58-4847-9621-04090c2f2739",
};

export const candlestickMockup = {
	command_location: "/stocks/candle",
	data: [
		{
			close: [
				5.119999885559082, 4.599999904632568, 4.550000190734863,
				4.519999980926514, 4.71999979019165, 4.559999942779541,
				4.659999847412109, 4.630000114440918, 4.579999923706055,
				5.110000133514404, 5.599999904632568, 5.070000171661377,
				5.130000114440918, 5.309999942779541, 5.590000152587891,
				5.449999809265137, 5.380000114440918, 5.909999847412109,
				6.449999809265137, 5.989999771118164, 6.289999961853027,
				5.170000076293945, 5.889999866485596, 5.800000190734863,
				5.559999942779541, 5.420000076293945, 5.630000114440918,
				5.519999980926514, 5.329999923706055, 5.099999904632568,
				4.789999961853027, 4.269999980926514, 4.179999828338623,
				4.420000076293945, 4.289999961853027, 4.570000171661377,
				4.53000020980835, 4.28000020980835, 4.130000114440918,
				4.429999828338623, 4.570000171661377, 4.599999904632568,
				4.260000228881836, 4.21999979019165, 4.5, 4.380000114440918,
				4.269999980926514, 4.150000095367432, 4.150000095367432,
				4.03000020980835, 4.059999942779541, 4, 3.869999885559082,
				4.150000095367432, 4.159999847412109, 4.119999885559082,
				4.039999961853027, 4.110000133514404, 4.099999904632568,
				4.150000095367432, 4.139999866485596, 4.75, 4.46999979019165,
				4.559999942779541, 4.639999866485596, 5.309999942779541,
				5.539999961853027, 5.599999904632568, 5.349999904632568,
				5.389999866485596, 5.690000057220459, 5.190000057220459,
				5.409999847412109, 5.539999961853027, 5.599999904632568,
				6.519999980926514, 6.300000190734863, 5.880000114440918,
				6.070000171661377, 7.039999961853027, 6.599999904632568,
				7.019999980926514, 6.420000076293945, 6.260000228881836,
				5.940000057220459, 5.789999961853027, 5.539999961853027,
				5.519999980926514, 5.760000228881836, 5.71999979019165,
				5.670000076293945, 5.320000171661377, 5.210000038146973,
				4.78000020980835, 4.610000133514404, 4.880000114440918,
				4.909999847412109, 4.860000133514404, 4.710000038146973,
				4.650000095367432, 4.650000095367432, 4.130000114440918,
				4.059999942779541, 4.039999961853027, 4.139999866485596,
				4.050000190734863, 4.079999923706055, 3.5399999618530273,
				2.9600000381469727, 2.7799999713897705, 3.0399999618530273,
				3.5399999618530273, 3.0899999141693115, 3, 3.119999885559082,
				2.9700000286102295, 2.75, 2.7899999618530273, 2.609999895095825,
				2.5199999809265137, 2.359999895095825, 2.1500000953674316,
				2.3399999141693115, 2.309999942779541, 2.4600000381469727,
				2.490000009536743, 3.7699999809265137, 3.509999990463257,
				3.130000114440918, 2.940000057220459, 2.9700000286102295,
				3.109999895095825, 2.9800000190734863, 3.259999990463257,
				3.190000057220459, 3.3499999046325684, 3.809999942779541,
				4.579999923706055, 4.489999771118164, 4.449999809265137,
				4.269999980926514, 4.150000095367432, 4.320000171661377,
				3.630000114440918, 3.509999990463257, 3.559999942779541,
				3.9800000190734863, 3.859999895095825, 4.090000152587891,
				3.9200000762939458, 3.190000057220459, 2.859999895095825,
				2.7799999713897705, 2.8499999046325684, 2.799999952316284,
				2.680000066757202, 2.5899999141693115, 2.559999942779541,
				2.509999990463257, 2.390000104904175, 2.2899999618530273,
				2.1600000858306885, 2.119999885559082, 2.009999990463257,
				1.9800000190734863, 2.009999990463257, 2.049999952316284,
				2.140000104904175, 2.200000047683716, 2.2899999618530273,
				2.180000066757202, 2.180000066757202, 2.3299999237060547,
				3.059999942779541, 2.9700000286102295, 2.9800000190734863,
				3.509999990463257, 4.420000076293945, 4.960000038146973,
				19.899999618530273, 8.630000114440918, 13.260000228881836,
				13.300000190734863, 7.820000171661377, 8.970000267028809,
				7.090000152587891, 6.829999923706055, 6.179999828338623, 5.5,
				5.800000190734863, 5.610000133514404, 5.590000152587891,
				5.650000095367432, 5.550000190734863, 5.510000228881836,
				5.699999809265137, 6.550000190734863, 7.699999809265137,
				9.09000015258789, 8.289999961853027, 8.010000228881836,
				9.18000030517578, 8.930000305175781, 8.579999923706055,
				8.029999732971191, 8.050000190734863, 9.289999961853027, 10.5,
				9.850000381469728, 10.279999732971191, 11.15999984741211,
				14.039999961853027, 13.020000457763672, 13.5600004196167, 14,
				13.93000030517578, 12.489999771118164, 10.65999984741211,
				9.020000457763672, 10.9399995803833, 10.239999771118164,
				10.350000381469728, 10.350000381469728, 10.210000038146973,
				9.359999656677246, 10.609999656677246, 10.199999809265137,
				9.850000381469728, 9.789999961853027, 9.420000076293944,
				8.619999885559082, 8.84000015258789, 9.350000381469728,
				9.899999618530272, 9.329999923706056, 9.65999984741211,
				9.279999732971191, 9.779999732971191, 9.989999771118164,
				10.15999984741211, 11.5, 11.460000038146973, 10.850000381469728,
				10.199999809265137, 10.029999732971191, 9.710000038146973,
				9.390000343322754, 9.170000076293944, 9, 9.510000228881836,
				9.739999771118164, 10.050000190734863, 10.31999969482422,
				12.770000457763672, 12.979999542236328, 13.949999809265137,
				14.029999732971191, 12.640000343322754, 12.550000190734863,
				12.079999923706056, 13.68000030517578, 16.40999984741211,
				19.559999465942383, 26.520000457763672, 26.1200008392334,
				32.040000915527344, 62.54999923706055, 51.34000015258789,
				47.90999984741211, 55, 55.04999923706055, 49.34000015258789,
				42.810001373291016, 49.400001525878906, 57, 59.040000915527344,
				55.18000030517578, 60.72999954223633, 59.2599983215332,
				55.689998626708984, 58.27000045776367, 58.29999923706055,
				56.70000076293945, 54.060001373291016, 58.11000061035156,
				56.43000030517578, 56.68000030517578, 54.220001220703125,
				51.959999084472656, 49.959999084472656, 45.06999969482422,
				47.939998626708984, 46.189998626708984, 42.61000061035156,
				39.349998474121094, 33.43000030517578, 36, 34.959999084472656,
				34.619998931884766, 43.09000015258789, 40.779998779296875,
				37.2400016784668, 36.9900016784668, 40.290000915527344,
				38.0099983215332, 38.900001525878906, 38.130001068115234,
				37.02000045776367, 35.20000076293945, 33.59000015258789,
				29.84000015258789, 33.5099983215332, 32.70000076293945,
				33.79999923706055, 31.75, 31.549999237060547, 33.06999969482422,
				33.470001220703125, 35.689998626708984, 37.15999984741211,
				36.54999923706055, 33.81999969482422, 34.40999984741211,
				36.779998779296875, 44.2599983215332, 43.959999084472656,
				40.310001373291016, 40.84000015258789, 43.33000183105469,
				47.130001068115234, 43.689998626708984, 44.380001068115234,
				44.02000045776367, 47.83000183105469, 47.400001525878906,
				48.52000045776367, 50.15999984741211, 51.689998626708984,
				47.29999923706055, 46.84000015258789, 46.040000915527344,
				44.20000076293945, 40.290000915527344, 38.81999969482422,
				40.08000183105469, 39.97999954223633, 40.0099983215332,
				39.29999923706055, 36.9900016784668, 35.540000915527344,
				38.060001373291016, 38.459999084472656, 36.77000045776367,
				37.060001373291016, 36.83000183105469, 38.13999938964844,
				37.189998626708984, 37.25, 36.81999969482422, 37.90999984741211,
				40.06999969482422, 40.7400016784668, 43.029998779296875,
				40.79999923706055, 40.880001068115234, 39.2400016784668,
				36.599998474121094, 36.83000183105469, 36.04999923706055,
				34.7599983215332, 35.22999954223633, 35.369998931884766,
				37.06999969482422, 38.790000915527344, 40.790000915527344,
				40.04999923706055, 41.70000076293945, 45.060001373291016,
				39.93000030517578, 38.290000915527344, 39.459999084472656, 40,
				42.68000030517578, 42.599998474121094, 42.130001068115234,
				40.40999984741211, 40.869998931884766, 41.2400016784668,
				39.15999984741211, 38.88999938964844, 37.630001068115234,
				36.84000015258789, 33.939998626708984, 28.56999969482422,
				30.280000686645508, 29.010000228881836, 28.790000915527344,
				31.040000915527344, 32.349998474121094, 29.459999084472656,
				27.440000534057617, 23.239999771118164, 24.5, 24.65999984741211,
				24.450000762939453, 29.1200008392334, 29.700000762939453,
				30.299999237060547, 28.68000030517578, 28.520000457763672,
				28.700000762939453, 27.719999313354492, 27.950000762939453,
				28.940000534057617, 27.200000762939453, 26.520000457763672,
				25.489999771118164, 22.75, 22.459999084472656, 22.989999771118164,
				22.780000686645508, 22.790000915527344, 22.719999313354492,
				20.65999984741211, 20.56999969482422, 18.84000015258789,
				18.31999969482422, 18.06999969482422, 17.969999313354492,
				16.639999389648438, 16.020000457763672, 15.9399995803833,
				14.520000457763672, 15.0600004196167, 16.059999465942383,
				16.860000610351562, 15.420000076293944, 14.869999885559082,
				15.350000381469728, 14.90999984741211, 16.43000030517578,
				18.940000534057617, 18.59000015258789, 18.809999465942383, 17.75,
				19.479999542236328, 19.670000076293945, 18.940000534057617,
				17.899999618530273, 16.469999313354492, 15.729999542236328,
				17.68000030517578, 17.65999984741211, 18.86000061035156,
				18.31999969482422, 18.530000686645508, 18.059999465942383,
				16.56999969482422, 15.210000038146973, 15.390000343322754,
				15.710000038146973, 15.31999969482422, 14.300000190734863,
				13.5600004196167, 14.479999542236328, 15.229999542236328,
				15.1899995803833, 15.800000190734863, 15.859999656677246,
				18.260000228881836, 20.739999771118164, 20.229999542236328,
				20.239999771118164, 29.329999923706055, 29.440000534057617,
				25.68000030517578, 24.63999938964844, 23.299999237060547,
				23.309999465942383, 21.209999084472656, 20.38999938964844,
				19.729999542236328, 18.239999771118164, 18.719999313354492,
				17.420000076293945, 18.530000686645508, 18.020000457763672,
				17.479999542236328, 18.68000030517578, 17.34000015258789,
				16.850000381469727, 16.520000457763672, 16.959999084472656, 15.5,
				15.850000381469728, 15.640000343322754, 15.300000190734863,
				15.260000228881836, 15.510000228881836, 15.720000267028809,
				14.6899995803833, 13.760000228881836, 12.520000457763672,
				11.84000015258789, 10.369999885559082, 11.199999809265137,
				11.8100004196167, 11.710000038146973, 12.899999618530272,
				12.760000228881836, 13.079999923706056, 12.029999732971191,
				11.579999923706056, 10.390000343322754, 11.880000114440918,
				12.229999542236328, 14.43000030517578, 14.34000015258789,
				12.8100004196167, 13.300000190734863, 12.449999809265137,
				11.949999809265137, 13.06999969482422, 13.520000457763672,
				12.779999732971191, 12.43000030517578, 11.479999542236328,
				11.920000076293944, 12.770000457763672, 11.789999961853027,
				12.529999732971191, 12.5, 12.600000381469728, 12.050000190734863,
				12.470000267028809, 14.130000114440918, 13.380000114440918,
				13.649999618530272, 13.550000190734863, 13.529999732971191,
				12.779999732971191, 12.56999969482422, 14.479999542236328,
				14.65999984741211, 14.949999809265137, 15.600000381469728,
				15.140000343322754, 15.0600004196167, 15.369999885559082,
				16.540000915527344, 16.360000610351562, 17.520000457763672, 17, 15.5,
				14.90999984741211, 14.029999732971191, 14.479999542236328,
				14.579999923706056, 14.5600004196167, 15.369999885559082,
				16.860000610351562, 18.209999084472656, 18.65999984741211,
				22.18000030517578, 23.959999084472656, 22.450000762939453,
				23.670000076293945, 25.459999084472656, 24.440000534057617,
				24.209999084472656, 24.809999465942383, 21.36000061035156,
				19.290000915527344, 18.020000457763672, 10.460000038146973,
				9.5600004196167, 9.579999923706056, 9.56999969482422, 9.170000076293944,
				9.470000267028809, 9.270000457763672, 9.119999885559082,
				8.579999923706055, 8.880000114440918, 8.1899995803833,
				8.390000343322754, 8.640000343322754, 9.720000267028809,
				10.220000267028809, 9.720000267028809, 9.90999984741211,
				9.880000114440918, 8.979999542236328, 9.18000030517578,
				8.710000038146973, 8.600000381469727, 7.849999904632568,
				7.989999771118164, 6.829999923706055, 7.449999809265137,
				7.670000076293945, 7.099999904632568, 6.96999979019165,
				6.880000114440918, 7.829999923706055, 7.329999923706055,
				7.119999885559082, 6.53000020980835, 6.349999904632568,
				6.119999885559082, 5.849999904632568, 6.039999961853027, 6,
				6.360000133514404, 6.510000228881836, 6.110000133514404,
				6.349999904632568, 6.489999771118164, 6.360000133514404, 6.75,
				6.639999866485596, 6.510000228881836, 6.510000228881836,
				6.659999847412109, 6.150000095367432, 5.809999942779541,
				5.699999809265137, 5.650000095367432, 5.329999923706055,
				5.619999885559082, 5.190000057220459, 6.130000114440918,
				7.199999809265137, 7.340000152587891, 7.949999809265137,
				7.53000020980835, 7.389999866485596, 7.590000152587891,
				7.269999980926514, 7.320000171661377, 7.639999866485596,
				7.510000228881836, 7.329999923706055, 7.429999828338623,
				7.230000019073486, 8.170000076293945, 8.170000076293945,
				7.449999809265137, 6.75, 6.050000190734863, 6.070000171661377,
				5.940000057220459, 5.949999809265137, 5.71999979019165, 5.75,
				5.599999904632568, 5.309999942779541, 4.889999866485596,
				5.079999923706055, 5.300000190734863, 4.909999847412109,
				4.400000095367432, 4.03000020980835, 3.839999914169311,
				4.139999866485596, 4.070000171661377, 3.930000066757202,
				4.090000152587891, 3.9600000381469727, 3.849999904632568,
				3.930000066757202, 4.059999942779541, 4.920000076293945,
				5.019999980926514, 5.059999942779541, 6.070000171661377,
				5.650000095367432, 5.519999980926514, 5.519999980926514,
				5.659999847412109, 5.5, 5.329999923706055, 5.28000020980835,
				5.510000228881836, 5.010000228881836, 5.349999904632568,
				5.710000038146973, 6.079999923706055, 6.079999923706055,
				6.800000190734863, 6.179999828338623, 5.71999979019165,
				5.360000133514404, 4.900000095367432, 4.679999828338623, 4.5,
				5.170000076293945, 5.25, 5.239999771118164, 6.099999904632568,
				6.260000228881836, 6.230000019073486, 6.199999809265137,
				7.610000133514404, 7.139999866485596, 6.570000171661377,
				6.099999904632568, 6.579999923706055, 6.25, 6.010000228881836,
				5.840000152587891, 5.650000095367432, 5.380000114440918,
				5.460000038146973, 4.639999866485596, 4.210000038146973,
				4.389999866485596, 4.179999828338623, 4.269999980926514,
				4.409999847412109, 4.340000152587891, 4.46999979019165,
				4.46999979019165, 4.550000190734863, 5.150000095367432, 5,
				4.96999979019165, 5.010000228881836, 5.110000133514404,
				3.910000085830689, 4.050000190734863, 4.900000095367432,
				5.239999771118164, 5.429999828338623, 5.340000152587891,
				5.460000038146973, 5.119999885559082, 5.199999809265137,
				5.050000190734863, 5.099999904632568, 4.96999979019165,
				4.989999771118164, 4.960000038146973, 5.150000095367432,
				5.190000057220459, 5.369999885559082, 5.5, 5.650000095367432, 5.5,
				5.739999771118164, 5.920000076293945, 5.889999866485596,
				5.900000095367432, 5.539999961853027, 5.489999771118164,
				5.340000152587891, 5.199999809265137, 5.139999866485596,
				4.960000038146973, 5.125,
			],
			decreasing: {
				line: {
					width: 0.8,
				},
			},
			high: [
				5.840000152587891, 5.03000020980835, 4.650000095367432,
				4.619999885559082, 4.929999828338623, 4.980000019073486, 4.75,
				4.789999961853027, 4.679999828338623, 5.139999866485596,
				5.650000095367432, 5.679999828338623, 5.380000114440918,
				5.369999885559082, 5.650000095367432, 5.989999771118164,
				5.400000095367432, 6.150000095367432, 6.840000152587891,
				6.190000057220459, 7.349999904632568, 5.650000095367432,
				5.929999828338623, 5.929999828338623, 6.059999942779541,
				5.630000114440918, 5.71999979019165, 6.25, 5.480000019073486,
				5.400000095367432, 5.119999885559082, 4.590000152587891,
				4.690000057220459, 4.440000057220459, 4.360000133514404,
				4.599999904632568, 4.690000057220459, 4.639999866485596,
				4.289999961853027, 4.519999980926514, 4.599999904632568,
				4.599999904632568, 4.820000171661377, 4.269999980926514,
				4.579999923706055, 4.480000019073486, 4.369999885559082,
				4.230000019073486, 4.239999771118164, 4.170000076293945,
				4.179999828338623, 4.179999828338623, 4.03000020980835, 4.25,
				4.199999809265137, 4.340000152587891, 4.150000095367432,
				4.199999809265137, 4.239999771118164, 4.170000076293945,
				4.239999771118164, 5.260000228881836, 4.739999771118164,
				4.849999904632568, 4.760000228881836, 5.769999980926514,
				5.630000114440918, 5.849999904632568, 5.570000171661377,
				5.46999979019165, 5.78000020980835, 5.679999828338623,
				5.449999809265137, 5.579999923706055, 5.670000076293945,
				7.099999904632568, 6.539999961853027, 6.449999809265137,
				6.179999828338623, 7.710000038146973, 7.139999866485596,
				7.019999980926514, 6.949999809265137, 6.539999961853027,
				6.360000133514404, 6.070000171661377, 5.869999885559082,
				5.869999885559082, 5.880000114440918, 5.789999961853027,
				5.739999771118164, 5.480000019073486, 5.320000171661377,
				5.289999961853027, 4.900000095367432, 4.929999828338623,
				5.039999961853027, 4.940000057220459, 4.949999809265137,
				4.800000190734863, 4.659999847412109, 4.360000133514404,
				4.269999980926514, 4.110000133514404, 4.179999828338623,
				4.179999828338623, 4.090000152587891, 3.910000085830689,
				3.200000047683716, 2.9600000381469727, 3.2899999618530273,
				3.880000114440918, 3.309999942779541, 3.130000114440918,
				3.1500000953674316, 3.1500000953674316, 2.930000066757202,
				2.9000000953674316, 2.740000009536743, 2.680000066757202,
				2.490000009536743, 2.319999933242798, 2.5899999141693115,
				2.430000066757202, 2.569999933242798, 2.619999885559082,
				4.389999866485596, 4.03000020980835, 3.240000009536743,
				3.069999933242798, 2.990000009536743, 3.390000104904175,
				3.0399999618530273, 3.369999885559082, 3.380000114440918,
				3.369999885559082, 3.849999904632568, 5, 4.849999904632568,
				4.619999885559082, 4.449999809265137, 4.429999828338623,
				4.340000152587891, 4.21999979019165, 3.759999990463257,
				3.740000009536743, 4.019999980926514, 4.329999923706055,
				4.099999904632568, 4.25, 4.010000228881836, 3.240000009536743,
				2.890000104904175, 2.950000047683716, 2.8499999046325684,
				2.740000009536743, 2.75, 2.6500000953674316, 2.5999999046325684,
				2.630000114440918, 2.4600000381469727, 2.299999952316284,
				2.2200000286102295, 2.200000047683716, 2.0299999713897705,
				2.2300000190734863, 2.109999895095825, 2.2100000381469727,
				2.2699999809265137, 2.390000104904175, 2.380000114440918,
				2.319999933242798, 2.549999952316284, 3.200000047683716,
				3.3399999141693115, 3.059999942779541, 3.740000009536743,
				4.880000114440918, 5.190000057220459, 20.36000061035156, 16.5, 16,
				17.25, 10.100000381469728, 9.770000457763672, 8.739999771118164,
				8.270000457763672, 6.889999866485596, 5.809999942779541,
				6.590000152587891, 5.849999904632568, 5.96999979019165,
				6.050000190734863, 5.619999885559082, 6.25, 5.769999980926514,
				6.679999828338623, 7.860000133514404, 9.829999923706056, 11,
				9.010000228881836, 9.449999809265137, 9.399999618530272,
				9.140000343322754, 8.59000015258789, 8.270000457763672,
				9.479999542236328, 10.770000457763672, 12.470000267028809,
				10.869999885559082, 11.399999618530272, 14.489999771118164,
				13.619999885559082, 13.65999984741211, 14.539999961853027,
				14.18000030517578, 13.1899995803833, 11.93000030517578,
				11.210000038146973, 11.31999969482422, 11.529999732971191,
				10.760000228881836, 10.520000457763672, 10.470000267028809,
				10.260000228881836, 11.25, 10.5, 10.18000030517578, 10.010000228881836,
				9.739999771118164, 9.489999771118164, 9.119999885559082,
				9.8100004196167, 10.229999542236328, 10.029999732971191,
				9.8100004196167, 9.710000038146973, 9.8100004196167, 10.649999618530272,
				10.380000114440918, 11.960000038146973, 12.220000267028809,
				11.390000343322754, 11.039999961853027, 10.18000030517578,
				10.119999885559082, 9.75, 9.56999969482422, 9.399999618530272,
				9.789999961853027, 10.149999618530272, 10.479999542236328,
				10.630000114440918, 14.199999809265137, 14.34000015258789,
				14.380000114440918, 14.670000076293944, 13.3100004196167,
				12.989999771118164, 12.84000015258789, 13.960000038146973,
				16.670000076293945, 19.950000762939453, 29.760000228881836,
				36.720001220703125, 33.529998779296875, 72.62000274658203,
				68.80000305175781, 57.47999954223633, 59.68000030517578,
				60.619998931884766, 53.38999938964844, 51.5, 49.599998474121094,
				60.54999923706055, 64.70999908447266, 57.34000015258789,
				63.83000183105469, 64.95999908447266, 63.0099983215332,
				58.7400016784668, 61.099998474121094, 58.7599983215332,
				56.290000915527344, 59.36000061035156, 61, 58.18000030517578,
				57.709999084472656, 53.25, 55.06999969482422, 48.9900016784668,
				49.790000915527344, 48.91999816894531, 46.54999923706055,
				42.13999938964844, 39.130001068115234, 37.400001525878906,
				38.54999923706055, 35.34000015258789, 44.38999938964844,
				46.54999923706055, 41.7400016784668, 38.400001525878906,
				40.849998474121094, 40.29999923706055, 39.560001373291016, 40.25,
				39.189998626708984, 38.47999954223633, 35.209999084472656,
				35.2400016784668, 34.119998931884766, 33.58000183105469,
				35.380001068115234, 37.15999984741211, 31.90999984741211,
				34.099998474121094, 34.47999954223633, 36.18000030517578,
				38.779998779296875, 38.70000076293945, 36.779998779296875,
				34.599998474121094, 37.93000030517578, 48.20000076293945,
				48.29999923706055, 44.779998779296875, 41.58000183105469,
				45.709999084472656, 47.15999984741211, 47.849998474121094,
				44.900001525878906, 44.79999923706055, 47.93000030517578,
				49.400001525878906, 49, 51.70000076293945, 52.790000915527344,
				51.54999923706055, 47.7400016784668, 48.689998626708984,
				46.380001068115234, 43.33000183105469, 41.4900016784668,
				40.56999969482422, 41.849998474121094, 40.52000045776367,
				40.630001068115234, 39.130001068115234, 38.2599983215332,
				41.779998779296875, 40.130001068115234, 39.029998779296875,
				38.099998474121094, 37.650001525878906, 38.54999923706055,
				38.779998779296875, 38.65999984741211, 37.56999969482422,
				38.150001525878906, 41.099998474121094, 41.790000915527344,
				43.630001068115234, 44.439998626708984, 41.75, 41.939998626708984,
				37.66999816894531, 37.849998474121094, 37.400001525878906,
				36.790000915527344, 36.06999969482422, 36.630001068115234,
				37.189998626708984, 38.79999923706055, 44.209999084472656,
				41.29999923706055, 41.970001220703125, 45.95000076293945,
				42.599998474121094, 40.869998931884766, 40.20000076293945,
				40.439998626708984, 43.22999954223633, 44.43000030517578, 44,
				42.400001525878906, 41.380001068115234, 42.9900016784668,
				42.029998779296875, 39.33000183105469, 38.15999984741211,
				38.43000030517578, 37.04999923706055, 34.939998626708984,
				31.219999313354492, 31.059999465942383, 30.469999313354492,
				31.68000030517578, 33.91999816894531, 32.95000076293945,
				29.93000030517578, 27.6299991607666, 25.1200008392334,
				25.280000686645508, 25.8700008392334, 30.709999084472656,
				30.700000762939453, 32.22999954223633, 30.479999542236328,
				29.43000030517578, 29.38999938964844, 29.739999771118164,
				28.350000381469727, 30.190000534057617, 29.399999618530273,
				28.1299991607666, 26.670000076293945, 25.299999237060547,
				23.770000457763672, 24.299999237060547, 22.8700008392334, 23.75,
				23.36000061035156, 23.149999618530273, 21.079999923706055,
				19.88999938964844, 19.420000076293945, 20.15999984741211,
				18.559999465942383, 17.290000915527344, 16.6200008392334,
				18.15999984741211, 16.59000015258789, 15.25, 16.25, 18.709999084472656,
				17.06999969482422, 15.850000381469728, 15.699999809265137, 16,
				16.81999969482422, 19, 20.959999084472656, 19.65999984741211,
				19.36000061035156, 19.549999237060547, 20.579999923706055,
				20.209999084472656, 19.200000762939453, 18.1299991607666,
				17.020000457763672, 17.770000457763672, 17.860000610351562,
				19.34000015258789, 19.43000030517578, 18.690000534057617,
				18.700000762939453, 18.31999969482422, 17.100000381469727,
				16.260000228881836, 16.270000457763672, 15.8100004196167,
				15.399999618530272, 14.15999984741211, 14.6899995803833,
				15.6899995803833, 15.609999656677246, 15.899999618530272,
				16.549999237060547, 18.90999984741211, 22.350000381469727,
				20.56999969482422, 21.700000762939453, 29.729999542236328,
				34.33000183105469, 29.229999542236328, 25.920000076293945,
				25.280000686645508, 23.75, 23.959999084472656, 21.920000076293945,
				20.940000534057617, 19.700000762939453, 18.81999969482422,
				19.010000228881836, 18.579999923706055, 18.690000534057617,
				18.190000534057617, 18.920000076293945, 18.65999984741211,
				18.06999969482422, 17.610000610351562, 17.030000686645508,
				17.09000015258789, 16.25, 16.1299991607666, 16.049999237060547,
				15.489999771118164, 16.110000610351562, 15.90999984741211,
				15.789999961853027, 14.84000015258789, 13.630000114440918,
				14.010000228881836, 11.649999618530272, 13.710000038146973,
				12.489999771118164, 12.65999984741211, 12.920000076293944,
				14.220000267028809, 13.5, 13.350000381469728, 12.020000457763672,
				11.390000343322754, 11.880000114440918, 12.880000114440918,
				14.470000267028809, 16.1299991607666, 14.31999969482422,
				13.539999961853027, 13.0600004196167, 12.579999923706056, 13.25, 14.25,
				13.529999732971191, 12.729999542236328, 12.199999809265137,
				12.050000190734863, 12.970000267028809, 12.5, 12.710000038146973,
				12.93000030517578, 13.220000267028809, 12.8100004196167,
				12.56999969482422, 14.75, 14.300000190734863, 13.890000343322754,
				13.850000381469728, 14.3100004196167, 13.579999923706056,
				13.039999961853027, 14.6899995803833, 15.31999969482422,
				14.989999771118164, 16.139999389648438, 15.93000030517578,
				15.68000030517578, 15.390000343322754, 16.959999084472656,
				17.81999969482422, 17.729999542236328, 18.3700008392334,
				16.8799991607666, 15.56999969482422, 14.729999542236328,
				14.539999961853027, 15.289999961853027, 14.869999885559082, 15.5,
				16.989999771118164, 18.270000457763672, 19.75, 22.770000457763672, 27.5,
				23.850000381469727, 23.799999237060547, 26.079999923706055,
				27.200000762939453, 24.489999771118164, 26.15999984741211,
				25.450000762939453, 22.09000015258789, 18.959999084472656,
				13.050000190734863, 10.9399995803833, 9.899999618530272,
				9.9399995803833, 9.670000076293944, 9.609999656677246,
				9.640000343322754, 9.279999732971191, 9.029999732971191,
				9.170000076293944, 8.75, 8.460000038146973, 8.720000267028809,
				9.729999542236328, 10.75, 9.890000343322754, 9.93000030517578,
				10.390000343322754, 9.68000030517578, 9.350000381469728,
				9.31999969482422, 8.960000038146973, 8.65999984741211,
				8.140000343322754, 7.96999979019165, 7.519999980926514,
				7.710000038146973, 7.610000133514404, 7.28000020980835,
				6.949999809265137, 8.130000114440918, 7.550000190734863,
				7.619999885559082, 7.099999904632568, 6.929999828338623,
				6.480000019073486, 6.239999771118164, 6.28000020980835,
				6.349999904632568, 6.480000019073486, 6.800000190734863, 6.5,
				6.579999923706055, 6.570000171661377, 6.550000190734863, 7,
				7.110000133514404, 6.929999828338623, 6.699999809265137, 7.25,
				6.849999904632568, 6.329999923706055, 5.849999904632568,
				5.849999904632568, 5.610000133514404, 5.619999885559082,
				5.389999866485596, 6.269999980926514, 7.28000020980835,
				8.350000381469727, 8.1899995803833, 7.800000190734863,
				7.420000076293945, 7.840000152587891, 7.559999942779541,
				7.510000228881836, 7.989999771118164, 7.739999771118164,
				7.440000057220459, 7.619999885559082, 7.480000019073486,
				9.149999618530272, 8.630000114440918, 8.539999961853027,
				7.480000019073486, 7.130000114440918, 6.699999809265137,
				6.130000114440918, 6, 6.510000228881836, 5.880000114440918,
				6.050000190734863, 5.730000019073486, 5.320000171661377,
				5.150000095367432, 5.369999885559082, 4.980000019073486,
				4.820000171661377, 4.210000038146973, 4.130000114440918,
				4.190000057220459, 4.090000152587891, 4.389999866485596,
				4.159999847412109, 4.059999942779541, 3.990000009536743,
				4.019999980926514, 4.079999923706055, 4.980000019073486,
				5.349999904632568, 5.139999866485596, 6.170000076293945,
				6.550000190734863, 5.650000095367432, 5.809999942779541,
				5.920000076293945, 5.949999809265137, 5.46999979019165,
				5.610000133514404, 5.619999885559082, 5.340000152587891,
				5.349999904632568, 5.800000190734863, 6.449999809265137,
				6.769999980926514, 7.329999923706055, 6.960000038146973,
				6.130000114440918, 5.880000114440918, 5.210000038146973,
				4.909999847412109, 4.619999885559082, 5.269999980926514,
				5.489999771118164, 5.53000020980835, 6.199999809265137,
				6.789999961853027, 6.650000095367432, 6.239999771118164,
				8.1899995803833, 8.529999732971191, 7.110000133514404,
				6.369999885559082, 6.690000057220459, 6.75, 6.349999904632568,
				6.130000114440918, 5.920000076293945, 5.619999885559082,
				5.559999942779541, 5.510000228881836, 4.610000133514404,
				4.650000095367432, 4.340000152587891, 4.380000114440918,
				4.449999809265137, 4.75, 4.679999828338623, 4.539999961853027,
				4.579999923706055, 5.5, 5.210000038146973, 5.159999847412109,
				5.059999942779541, 5.150000095367432, 4.449999809265137,
				4.090000152587891, 5.159999847412109, 5.369999885559082,
				5.630000114440918, 5.739999771118164, 5.659999847412109,
				5.760000228881836, 5.300000190734863, 5.230000019073486,
				5.170000076293945, 5.019999980926514, 5.070000171661377,
				5.03000020980835, 5.340000152587891, 5.320000171661377,
				5.559999942779541, 5.539999961853027, 5.690000057220459,
				5.739999771118164, 5.820000171661377, 6.050000190734863,
				6.110000133514404, 6.03000020980835, 5.949999809265137,
				5.610000133514404, 5.480000019073486, 5.320000171661377,
				5.239999771118164, 5.139999866485596, 5.150000095367432,
			],
			increasing: {
				line: {
					width: 0.8,
				},
			},
			low: [
				4.909999847412109, 4.510000228881836, 4.079999923706055,
				4.440000057220459, 4.630000114440918, 4.5, 4.590000152587891,
				4.550000190734863, 4.559999942779541, 4.699999809265137,
				5.110000133514404, 5.019999980926514, 4.829999923706055,
				5.019999980926514, 5.329999923706055, 5.090000152587891, 5,
				5.639999866485596, 6.170000076293945, 5.559999942779541,
				6.28000020980835, 5, 5.21999979019165, 5.300000190734863, 5.5,
				5.309999942779541, 5.340000152587891, 5.460000038146973,
				5.210000038146973, 5.039999961853027, 4.510000228881836,
				4.170000076293945, 4.150000095367432, 3.75, 4.210000038146973,
				4.260000228881836, 4.46999979019165, 4.130000114440918,
				4.079999923706055, 4.199999809265137, 4.179999828338623,
				4.380000114440918, 4.25, 4.099999904632568, 4.21999979019165,
				4.309999942779541, 4.199999809265137, 4.070000171661377,
				4.079999923706055, 4, 4, 3.9600000381469727, 3.809999942779541,
				3.839999914169311, 3.950000047683716, 4.059999942779541,
				3.950000047683716, 3.859999895095825, 4.059999942779541,
				4.070000171661377, 3.990000009536743, 4.090000152587891,
				4.349999904632568, 4.539999961853027, 4.559999942779541,
				4.820000171661377, 5.050000190734863, 5.429999828338623,
				5.130000114440918, 5.179999828338623, 5.300000190734863,
				5.179999828338623, 4.940000057220459, 5.210000038146973,
				5.329999923706055, 5.699999809265137, 6.110000133514404,
				5.760000228881836, 5.789999961853027, 6.460000038146973,
				6.480000019073486, 6.230000019073486, 6.369999885559082,
				6.110000133514404, 5.880000114440918, 5.599999904632568,
				5.510000228881836, 5.519999980926514, 5.420000076293945,
				5.579999923706055, 5.570000171661377, 5.010000228881836,
				5.139999866485596, 4.739999771118164, 4.360000133514404, 4.5,
				4.610000133514404, 4.75, 4.699999809265137, 4.630000114440918,
				4.420000076293945, 4.050000190734863, 4.050000190734863,
				3.940000057220459, 4.019999980926514, 4.039999961853027, 4,
				3.5199999809265137, 2.6600000858306885, 2.759999990463257,
				2.799999952316284, 3.2300000190734863, 3.049999952316284,
				2.9800000190734863, 2.8399999141693115, 2.950000047683716,
				2.680000066757202, 2.609999895095825, 2.5799999237060547,
				2.4800000190734863, 2.2799999713897705, 2.109999895095825,
				2.299999952316284, 2.240000009536743, 2.2699999809265137,
				2.3299999237060547, 3.2300000190734863, 3.3399999141693115, 3,
				2.9200000762939453, 2.7799999713897705, 3.049999952316284,
				2.9000000953674316, 3.009999990463257, 3.1500000953674316,
				3.2100000381469727, 3.4100000858306885, 4.150000095367432,
				4.199999809265137, 4.360000133514404, 3.990000009536743,
				4.090000152587891, 3.950000047683716, 3.5, 3.299999952316284,
				3.3299999237060547, 3.609999895095825, 3.75, 3.7699999809265137,
				3.869999885559082, 3, 2.759999990463257, 2.7200000286102295,
				2.740000009536743, 2.759999990463257, 2.5799999237060547,
				2.5199999809265137, 2.5399999618530273, 2.4800000190734863,
				2.359999895095825, 2.2799999713897705, 2.130000114440918,
				2.0799999237060547, 2, 1.909999966621399, 1.9700000286102295,
				2.0199999809265137, 2.069999933242798, 2.1500000953674316,
				2.240000009536743, 2.130000114440918, 2.130000114440918,
				2.180000066757202, 2.569999933242798, 2.75, 2.8499999046325684,
				2.809999942779541, 3.849999904632568, 4.369999885559082,
				11.010000228881836, 6.510000228881836, 11.600000381469728,
				12.90999984741211, 6, 7.889999866485596, 7, 6.519999980926514, 5.75,
				5.260000228881836, 5.449999809265137, 5.46999979019165,
				5.519999980926514, 5.489999771118164, 5.320000171661377,
				5.460000038146973, 5.510000228881836, 5.75, 6.010000228881836,
				6.989999771118164, 7.849999904632568, 7.630000114440918,
				8.420000076293945, 8.510000228881836, 8.5, 7.5, 7.630000114440918,
				8.3100004196167, 9.220000267028809, 9.510000228881836,
				9.899999618530272, 9.9399995803833, 11.850000381469728,
				12.34000015258789, 13, 13.56999969482422, 13.279999732971191,
				11.760000228881836, 10.369999885559082, 8.930000305175781,
				8.949999809265137, 10.010000228881836, 10.09000015258789,
				9.760000228881836, 10.050000190734863, 9.149999618530272,
				9.720000267028809, 10, 9.850000381469728, 9.5, 9.239999771118164,
				8.510000228881836, 8.3100004196167, 8.899999618530273,
				9.579999923706056, 9.09000015258789, 9.380000114440918,
				9.010000228881836, 9.140000343322754, 9.789999961853027,
				9.960000038146973, 10.56999969482422, 11.220000267028809,
				10.649999618530272, 10.09000015258789, 9.880000114440918,
				9.609999656677246, 9.050000190734863, 9.079999923706056,
				8.930000305175781, 9.140000343322754, 9.5600004196167,
				9.600000381469728, 10.020000457763672, 10.640000343322754,
				12.56999969482422, 13.390000343322754, 13.56999969482422,
				12.140000343322754, 12.029999732971191, 12.050000190734863,
				12.170000076293944, 13.550000190734863, 17.260000228881836,
				18.309999465942383, 24.170000076293945, 28.530000686645508,
				35.59000015258789, 37.65999984741211, 46.040000915527344, 51.5,
				52.77000045776367, 48.119998931884766, 39.709999084472656,
				42.0099983215332, 51.52000045776367, 56.72999954223633,
				51.86000061035156, 52.97999954223633, 56.849998474121094,
				53.43000030517578, 51.04999923706055, 56.79999923706055,
				55.65999984741211, 52.970001220703125, 54.33000183105469,
				56.18000030517578, 54.650001525878906, 52.529998779296875,
				47.77000045776367, 49.70000076293945, 42.79999923706055,
				38.7599983215332, 45.81999969482422, 42.06999969482422,
				38.70000076293945, 33.2400016784668, 32.13999938964844,
				34.29999923706055, 31.149999618530273, 35.130001068115234,
				40.11000061035156, 37.150001525878906, 34.689998626708984,
				37.56999969482422, 37.060001373291016, 36.08000183105469,
				37.470001220703125, 36.790000915527344, 35.0099983215332,
				32.779998779296875, 29.809999465942383, 28.90999984741211,
				31.56999969482422, 32.349998474121094, 31.440000534057617,
				29.399999618530273, 30.75, 31.8799991607666, 32.709999084472656,
				34.59000015258789, 36.4900016784668, 33.33000183105469,
				32.20000076293945, 34.400001525878906, 36.349998474121094,
				43.16999816894531, 40.06999969482422, 39.38999938964844,
				41.279998779296875, 44.04999923706055, 43.04999923706055,
				42.369998931884766, 42.470001220703125, 44.880001068115234,
				45.72999954223633, 45.36000061035156, 48.95000076293945,
				50.349998474121094, 46.959999084472656, 43.77000045776367,
				45.95000076293945, 44.20000076293945, 38.529998779296875,
				37.650001525878906, 37.7400016784668, 39.849998474121094, 39.25,
				39.209999084472656, 36.880001068115234, 35.369998931884766,
				33.7400016784668, 37.75, 36.33000183105469, 36.189998626708984,
				35.63999938964844, 36.599998474121094, 37.060001373291016,
				36.29999923706055, 36.220001220703125, 36.119998931884766, 37.75,
				39.779998779296875, 40.4900016784668, 40.7400016784668,
				40.29999923706055, 38.79999923706055, 35.959999084472656,
				35.779998779296875, 35.779998779296875, 34.58000183105469,
				34.86000061035156, 34.529998779296875, 35.38999938964844,
				36.630001068115234, 38.880001068115234, 39.11000061035156,
				39.93000030517578, 41.77000045776367, 39.25, 38.04999923706055, 37.5,
				39.119998931884766, 40.209999084472656, 41.22999954223633,
				42.02000045776367, 39.779998779296875, 39.65999984741211,
				40.290000915527344, 38.06999969482422, 37.54999923706055,
				36.130001068115234, 35.91999816894531, 32.75, 26.850000381469727,
				27.010000228881836, 25.309999465942383, 27.149999618530273, 29.5,
				29.770000457763672, 29.309999465942383, 26, 22.459999084472656,
				20.799999237060547, 22.530000686645508, 24.079999923706055,
				23.649999618530273, 28.11000061035156, 29.049999237060547,
				28.040000915527344, 26.81999969482422, 27.010000228881836,
				27.59000015258789, 26.6200008392334, 27.68000030517578,
				27.11000061035156, 26.420000076293945, 24.63999938964844,
				22.36000061035156, 20.799999237060547, 22.440000534057617, 21.25,
				22.09000015258789, 22.049999237060547, 20.530000686645508,
				19.510000228881836, 17.799999237060547, 18.030000686645508,
				17.950000762939453, 16.219999313354492, 14.229999542236328,
				15.550000190734863, 15.649999618530272, 14.399999618530272,
				13.399999618530272, 15, 16.520000457763672, 15.380000114440918,
				14.649999618530272, 14.739999771118164, 14.68000030517578,
				14.649999618530272, 16.139999389648438, 17.850000381469727,
				18.329999923706055, 17.65999984741211, 17.959999084472656,
				19.260000228881836, 18.469999313354492, 17.68000030517578,
				16.110000610351562, 15.619999885559082, 14.960000038146973,
				16.530000686645508, 17.610000610351562, 17.829999923706055,
				17.309999465942383, 17.799999237060547, 16.350000381469727,
				14.899999618530272, 14.380000114440918, 15.43000030517578,
				14.779999732971191, 14.270000457763672, 12.899999618530272,
				13.170000076293944, 14.229999542236328, 14.859999656677246,
				14.970000267028809, 15.279999732971191, 15.75, 18.18000030517578,
				18.86000061035156, 19.709999084472656, 20.530000686645508,
				26.40999984741211, 25.350000381469727, 23.260000228881836,
				22.34000015258789, 21.940000534057617, 21, 20.010000228881836,
				18.6299991607666, 18.1299991607666, 17.719999313354492,
				17.200000762939453, 16.940000534057617, 17.899999618530273,
				16.969999313354492, 17.100000381469727, 17.299999237060547,
				16.65999984741211, 16.100000381469727, 16.290000915527344,
				15.489999771118164, 15.25, 14.699999809265137, 15.220000267028809,
				14.609999656677246, 14.729999542236328, 14.68000030517578,
				14.359999656677246, 13.520000457763672, 12.43000030517578, 11.5,
				9.90999984741211, 9.699999809265137, 11.489999771118164, 11.5,
				11.84000015258789, 12.510000228881836, 12.65999984741211,
				11.43000030517578, 11.449999809265137, 10.300000190734863,
				10.399999618530272, 11.59000015258789, 12.399999618530272,
				13.93000030517578, 12.800000190734863, 12.300000190734863,
				12.18000030517578, 11.770000457763672, 11.8100004196167,
				12.710000038146973, 12.56999969482422, 12.06999969482422,
				11.09000015258789, 11.109999656677246, 11.539999961853027,
				11.43000030517578, 11.859999656677246, 12.220000267028809,
				12.369999885559082, 11.4399995803833, 11.93000030517578,
				12.3100004196167, 13.3100004196167, 12.800000190734863,
				12.90999984741211, 13.260000228881836, 12.5600004196167,
				12.15999984741211, 12.40999984741211, 13.84000015258789,
				14.06999969482422, 14.8100004196167, 14.899999618530272,
				14.729999542236328, 14.710000038146973, 15.529999732971191, 16.25,
				16.329999923706055, 16.950000762939453, 15.279999732971191,
				14.800000190734863, 13.9399995803833, 13.81999969482422,
				14.199999809265137, 14.039999961853027, 14.31999969482422,
				15.3100004196167, 16.780000686645508, 18.25, 16.5, 23.100000381469727,
				21.739999771118164, 20.729999542236328, 23.68000030517578,
				23.959999084472656, 22.670000076293945, 23.399999618530273,
				21.280000686645508, 19.1200008392334, 17.5, 10.300000190734863,
				9.470000267028809, 9.229999542236328, 9.3100004196167,
				8.960000038146973, 8.90999984741211, 9.029999732971191,
				8.680000305175781, 8.300000190734863, 8.350000381469727,
				8.170000076293945, 7.889999866485596, 8.239999771118164,
				8.779999732971191, 9.850000381469728, 9.449999809265137,
				9.210000038146973, 9.710000038146973, 8.979999542236328,
				8.850000381469727, 8.619999885559082, 8.460000038146973,
				7.730000019073486, 7.650000095367432, 6.809999942779541,
				6.980000019073486, 7.110000133514404, 6.900000095367432,
				6.820000171661377, 6.610000133514404, 6.96999979019165,
				7.039999961853027, 7.119999885559082, 6.400000095367432,
				6.269999980926514, 6.070000171661377, 5.619999885559082,
				5.46999979019165, 5.949999809265137, 6.090000152587891,
				6.369999885559082, 6.050000190734863, 6.130000114440918,
				6.21999979019165, 6.199999809265137, 6.269999980926514, 6.5,
				6.489999771118164, 6.360000133514404, 6.539999961853027,
				6.130000114440918, 5.789999961853027, 5.579999923706055,
				5.420000076293945, 5.170000076293945, 5.300000190734863,
				5.050000190734863, 5.349999904632568, 5.929999828338623,
				7.289999961853027, 7.460000038146973, 7.159999847412109,
				7.099999904632568, 7.340000152587891, 7.050000190734863,
				7.070000171661377, 7.28000020980835, 7.5, 7.119999885559082,
				7.079999923706055, 6.960000038146973, 7.210000038146973,
				7.920000076293945, 7.409999847412109, 6.679999828338623, 6,
				5.96999979019165, 5.809999942779541, 5.610000133514404,
				5.53000020980835, 5.610000133514404, 5.53000020980835,
				5.130000114440918, 4.78000020980835, 4.739999771118164,
				5.050000190734863, 4.110000133514404, 4.309999942779541, 4,
				3.809999942779541, 3.859999895095825, 3.900000095367432,
				3.869999885559082, 3.839999914169311, 3.859999895095825,
				3.7699999809265137, 3.7899999618530273, 3.910000085830689,
				4.309999942779541, 4.739999771118164, 4.769999980926514,
				5.059999942779541, 5.460000038146973, 5.260000228881836,
				5.449999809265137, 5.420000076293945, 5.400000095367432,
				5.28000020980835, 5.099999904632568, 5.099999904632568,
				4.949999809265137, 5, 5.269999980926514, 5.929999828338623,
				6.03000020980835, 6.050000190734863, 6.050000190734863,
				5.610000133514404, 5.139999866485596, 4.639999866485596,
				4.579999923706055, 4.389999866485596, 4.53000020980835,
				5.050000190734863, 5.210000038146973, 5.440000057220459,
				6.019999980926514, 5.909999847412109, 5.989999771118164,
				6.190000057220459, 7.110000133514404, 6.460000038146973,
				5.900000095367432, 6.139999866485596, 6.25, 6, 5.670000076293945,
				5.559999942779541, 5.300000190734863, 5.239999771118164,
				4.360000133514404, 4.150000095367432, 4.059999942779541,
				4.110000133514404, 4.139999866485596, 4.210000038146973,
				4.309999942779541, 4.340000152587891, 4.380000114440918,
				4.300000190734863, 4.460000038146973, 4.829999923706055,
				4.869999885559082, 4.869999885559082, 4.940000057220459,
				3.880000114440918, 3.940000057220459, 4.309999942779541,
				4.739999771118164, 5.25, 5.340000152587891, 5.389999866485596,
				4.900000095367432, 5.130000114440918, 4.980000019073486,
				4.920000076293945, 4.889999866485596, 4.909999847412109,
				4.639999866485596, 4.920000076293945, 5.050000190734863,
				5.159999847412109, 5.340000152587891, 5.380000114440918,
				5.320000171661377, 5.409999847412109, 5.71999979019165,
				5.659999847412109, 5.760000228881836, 5.53000020980835,
				5.400000095367432, 5.190000057220459, 5.079999923706055,
				5.03000020980835, 4.940000057220459, 4.869999885559082,
			],
			name: " AMC OHLC      ",
			open: [
				5.769999980926514, 5.019999980926514, 4.25, 4.46999979019165,
				4.800000190734863, 4.820000171661377, 4.690000057220459,
				4.679999828338623, 4.659999847412109, 4.800000190734863,
				5.480000019073486, 5.650000095367432, 5, 5.03000020980835,
				5.349999904632568, 5.269999980926514, 5.170000076293945,
				5.78000020980835, 6.269999980926514, 6.099999904632568,
				7.300000190734863, 5.53000020980835, 5.690000057220459,
				5.420000076293945, 6.019999980926514, 5.409999847412109,
				5.349999904632568, 6.199999809265137, 5.480000019073486,
				5.369999885559082, 4.989999771118164, 4.570000171661377,
				4.260000228881836, 3.910000085830689, 4.340000152587891,
				4.260000228881836, 4.690000057220459, 4.619999885559082,
				4.179999828338623, 4.489999771118164, 4.480000019073486,
				4.389999866485596, 4.760000228881836, 4.25, 4.369999885559082,
				4.329999923706055, 4.360000133514404, 4.210000038146973,
				4.150000095367432, 4.099999904632568, 4.079999923706055, 4,
				4.010000228881836, 3.849999904632568, 4.070000171661377,
				4.119999885559082, 4.079999923706055, 4.050000190734863,
				4.070000171661377, 4.079999923706055, 4.110000133514404,
				4.139999866485596, 4.650000095367432, 4.699999809265137,
				4.619999885559082, 5.099999904632568, 5.559999942779541,
				5.71999979019165, 5.570000171661377, 5.340000152587891,
				5.570000171661377, 5.679999828338623, 5.349999904632568,
				5.460000038146973, 5.449999809265137, 5.809999942779541,
				6.489999771118164, 6.329999923706055, 5.789999961853027,
				7.010000228881836, 6.940000057220459, 6.760000228881836,
				6.760000228881836, 6.539999961853027, 6.28000020980835,
				6.059999942779541, 5.840000152587891, 5.599999904632568,
				5.519999980926514, 5.670000076293945, 5.710000038146973,
				5.420000076293945, 5.21999979019165, 5.21999979019165,
				4.690000057220459, 4.639999866485596, 5.039999961853027,
				4.880000114440918, 4.800000190734863, 4.78000020980835,
				4.480000019073486, 4.300000190734863, 4.260000228881836,
				4.079999923706055, 4.130000114440918, 4.170000076293945,
				4.03000020980835, 3.900000095367432, 3.0899999141693115,
				2.8399999141693115, 2.869999885559082, 3.309999942779541,
				3.299999952316284, 3.0999999046325684, 2.9600000381469727,
				3.130000114440918, 2.9200000762939453, 2.880000114440918,
				2.690000057220459, 2.6500000953674316, 2.4800000190734863,
				2.299999952316284, 2.3399999141693115, 2.4000000953674316,
				2.3499999046325684, 2.430000066757202, 4.269999980926514,
				3.990000009536743, 3.2300000190734863, 3.069999933242798,
				2.9800000190734863, 3.390000104904175, 3.009999990463257,
				3.0799999237060547, 3.1600000858306885, 3.25, 3.509999990463257,
				4.159999847412109, 4.570000171661377, 4.539999961853027,
				4.409999847412109, 4.429999828338623, 4.079999923706055,
				4.010000228881836, 3.75, 3.450000047683716, 3.609999895095825,
				4.21999979019165, 3.7899999618530273, 4.039999961853027,
				4.010000228881836, 3.240000009536743, 2.869999885559082,
				2.799999952316284, 2.8499999046325684, 2.609999895095825,
				2.7300000190734863, 2.5799999237060547, 2.5899999141693115,
				2.630000114440918, 2.440000057220459, 2.299999952316284,
				2.1700000762939453, 2.200000047683716, 1.9900000095367432,
				2.0299999713897705, 2.0799999237060547, 2.0899999141693115,
				2.1600000858306885, 2.240000009536743, 2.3299999237060547,
				2.2200000286102295, 2.200000047683716, 2.799999952316284,
				3.2899999618530273, 3, 2.9100000858306885, 4.710000038146973,
				5.090000152587891, 20.34000015258789, 11.979999542236328,
				14.3100004196167, 17, 9.479999542236328, 8.850000381469727,
				8.699999809265137, 7.170000076293945, 6.880000114440918,
				5.809999942779541, 5.710000038146973, 5.619999885559082,
				5.71999979019165, 6.03000020980835, 5.579999923706055,
				5.840000152587891, 5.539999961853027, 5.929999828338623,
				6.96999979019165, 7.230000019073486, 10.890000343322754,
				8.1899995803833, 8.859999656677246, 9.140000343322754,
				8.949999809265137, 8.25, 8.079999923706055, 8.529999732971191,
				9.380000114440918, 11.020000457763672, 10.649999618530272,
				10.15999984741211, 12.18000030517578, 13.619999885559082,
				13.239999771118164, 14.34000015258789, 14.140000343322754,
				13.149999618530272, 11.460000038146973, 10.81999969482422,
				8.960000038146973, 11.270000457763672, 10.31999969482422,
				10.3100004196167, 10.399999618530272, 10.229999542236328,
				10.100000381469728, 10.399999618530272, 10.06999969482422,
				10.010000228881836, 9.600000381469728, 9.4399995803833,
				8.65999984741211, 9, 9.880000114440918, 10, 9.479999542236328,
				9.699999809265137, 9.25, 9.949999809265137, 10.09000015258789,
				10.6899995803833, 11.68000030517578, 10.850000381469728,
				10.949999809265137, 10.06999969482422, 10.109999656677246,
				9.630000114440918, 9.40999984741211, 9.329999923706056,
				9.31999969482422, 9.899999618530272, 9.93000030517578,
				10.029999732971191, 10.880000114440918, 13.3100004196167,
				13.670000076293944, 14.25, 12.949999809265137, 12.59000015258789,
				12.609999656677246, 12.380000114440918, 13.609999656677246,
				17.760000228881836, 18.61000061035156, 31.809999465942383,
				31.88999938964844, 37.52000045776367, 58.099998474121094,
				48.790000915527344, 52.380001068115234, 57.15999984741211,
				52.20000076293945, 47.93000030517578, 44.68000030517578,
				51.83000183105469, 58.38999938964844, 56.13999938964844, 54,
				61.2599983215332, 61.34000015258789, 54.099998474121094,
				57.040000915527344, 57.97999954223633, 55.75, 55.099998474121094,
				59.060001373291016, 56, 56.86000061035156, 52.77000045776367,
				53.459999084472656, 47.70000076293945, 40.95000076293945,
				48.369998931884766, 44.290000915527344, 40.56999969482422,
				38.79999923706055, 32.20000076293945, 37.83000183105469,
				32.95000076293945, 35.13999938964844, 41.79999923706055,
				40.15999984741211, 37.779998779296875, 38.31999969482422,
				39.9900016784668, 37.40999984741211, 38, 37.540000915527344,
				37.58000183105469, 35.15999984741211, 34.43000030517578,
				31.079999923706055, 33.41999816894531, 32.68000030517578,
				36.900001525878906, 31.579999923706055, 30.90999984741211,
				32.20000076293945, 33.849998474121094, 34.9900016784668,
				37.31999969482422, 36.59000015258789, 33.900001525878906,
				35.029998779296875, 37.189998626708984, 44.900001525878906,
				42.790000915527344, 40.0099983215332, 41.779998779296875,
				44.15999984741211, 47.15999984741211, 43.869998931884766,
				43.540000915527344, 45, 47.029998779296875, 46.22999954223633,
				49.150001525878906, 51.81999969482422, 50.900001525878906,
				46.43000030517578, 46.47999954223633, 46.099998474121094,
				41.95000076293945, 40.970001220703125, 38.5, 41.060001373291016,
				39.810001373291016, 40.119998931884766, 38.900001525878906, 37.25,
				35.189998626708984, 39.40999984741211, 38.900001525878906,
				36.86000061035156, 36.36000061035156, 36.779998779296875,
				37.91999816894531, 36.849998474121094, 37.25, 36.720001220703125,
				37.79999923706055, 40.20000076293945, 40.79999923706055,
				42.959999084472656, 40.650001525878906, 40.88999938964844,
				37.310001373291016, 36.22999954223633, 36.529998779296875,
				36.33000183105469, 35.09000015258789, 35.34000015258789,
				35.650001525878906, 37.619998931884766, 40.9900016784668,
				40.38999938964844, 41.15999984741211, 42.47999954223633,
				42.43000030517578, 38.790000915527344, 38, 39.599998474121094,
				40.349998474121094, 41.970001220703125, 42.34000015258789,
				42.38999938964844, 40.20000076293945, 41.5099983215332, 41.25,
				39.13999938964844, 36.290000915527344, 38.2599983215332,
				36.77000045776367, 34.709999084472656, 29.270000457763672,
				30.829999923706055, 28.100000381469727, 30.030000686645508,
				30.780000686645508, 31.75, 29.350000381469727, 27.489999771118164,
				20.90999984741211, 24.600000381469727, 25.350000381469727,
				24.38999938964844, 28.959999084472656, 29.399999618530273,
				29.940000534057617, 28.86000061035156, 28.350000381469727,
				28.18000030517578, 27.75, 27.90999984741211, 28.760000228881836,
				27.420000076293945, 26.670000076293945, 25.170000076293945,
				22.959999084472656, 23.61000061035156, 22.420000076293945,
				22.399999618530273, 22.86000061035156, 22.649999618530273,
				20.329999923706055, 19.790000915527344, 18.530000686645508,
				18.59000015258789, 17.770000457763672, 16.239999771118164,
				15.890000343322754, 16.209999084472656, 16.110000610351562,
				14.600000381469728, 15.140000343322754, 18.149999618530273,
				16.549999237060547, 15.039999961853027, 15, 15.619999885559082,
				14.899999618530272, 16.299999237060547, 17.899999618530273,
				18.6200008392334, 18.829999923706055, 18.049999237060547,
				19.350000381469727, 19.479999542236328, 18.989999771118164,
				17.360000610351562, 16.729999542236328, 14.970000267028809,
				17.709999084472656, 18, 19, 18.010000228881836, 18.6299991607666,
				18.049999237060547, 16.90999984741211, 15.149999618530272,
				15.720000267028809, 15.630000114440918, 15.3100004196167,
				14.050000190734863, 13.760000228881836, 14.510000228881836,
				14.90999984741211, 14.979999542236328, 15.6899995803833,
				15.880000114440918, 18.75, 20.049999237060547, 19.950000762939453,
				20.61000061035156, 30.030000686645508, 28.559999465942383,
				24.770000457763672, 25.1299991607666, 23.479999542236328,
				23.18000030517578, 20.649999618530273, 20.6299991607666,
				19.700000762939453, 18.030000686645508, 18.8799991607666,
				17.549999237060547, 18.270000457763672, 18.100000381469727,
				17.3799991607666, 18.399999618530273, 17.40999984741211,
				17.1200008392334, 16.389999389648438, 16.889999389648438,
				15.390000343322754, 15.710000038146973, 15.630000114440918,
				15.100000381469728, 15.15999984741211, 15.220000267028809,
				15.68000030517578, 14.65999984741211, 13.630000114440918,
				13.149999618530272, 11.5600004196167, 10.050000190734863,
				12.109999656677246, 11.899999618530272, 12.0600004196167,
				12.729999542236328, 12.770000457763672, 13.18000030517578,
				12.010000228881836, 11.329999923706056, 10.479999542236328, 11.75,
				12.649999618530272, 15.75, 14.010000228881836, 12.6899995803833,
				12.779999732971191, 12.420000076293944, 12.020000457763672,
				12.800000190734863, 13.369999885559082, 12.630000114440918,
				11.90999984741211, 11.520000457763672, 11.550000190734863,
				12.220000267028809, 11.859999656677246, 12.75, 12.5, 12.789999961853027,
				12.170000076293944, 12.579999923706056, 14, 13.06999969482422,
				13.399999618530272, 13.479999542236328, 13.550000190734863,
				12.68000030517578, 12.539999961853027, 13.890000343322754,
				14.489999771118164, 14.949999809265137, 15.050000190734863,
				14.949999809265137, 15.270000457763672, 15.600000381469728,
				17.40999984741211, 16.520000457763672, 17.899999618530273,
				16.700000762939453, 15.550000190734863, 14.65999984741211,
				14.229999542236328, 14.630000114440918, 14.3100004196167,
				14.329999923706056, 15.449999809265137, 17.200000762939453,
				18.979999542236328, 16.969999313354492, 24.059999465942383,
				23.200000762939453, 23.38999938964844, 24.06999969482422,
				26.940000534057617, 24.06999969482422, 24.010000228881836,
				24.59000015258789, 21.86000061035156, 18.040000915527344,
				11.329999923706056, 10.720000267028809, 9.59000015258789,
				9.779999732971191, 9.579999923706056, 9.039999961853027,
				9.59000015258789, 9.109999656677246, 9.029999732971191,
				8.779999732971191, 8.649999618530273, 8.069999694824219,
				8.300000190734863, 8.859999656677246, 10.010000228881836,
				9.630000114440918, 9.520000457763672, 9.75, 9.619999885559082,
				9.06999969482422, 9.229999542236328, 8.729999542236328,
				8.619999885559082, 7.71999979019165, 7.650000095367432,
				7.159999847412109, 7.300000190734863, 7.460000038146973,
				6.96999979019165, 6.840000152587891, 6.980000019073486,
				7.550000190734863, 7.380000114440918, 7.099999904632568,
				6.460000038146973, 6.400000095367432, 6.119999885559082,
				5.630000114440918, 6.210000038146973, 6.179999828338623,
				6.599999904632568, 6.409999847412109, 6.199999809265137,
				6.349999904632568, 6.460000038146973, 6.300000190734863,
				6.539999961853027, 6.829999923706055, 6.550000190734863,
				6.820000171661377, 6.760000228881836, 6.260000228881836, 5.75,
				5.829999923706055, 5.559999942779541, 5.380000114440918,
				5.369999885559082, 5.5, 6.010000228881836, 8.020000457763672,
				7.829999923706055, 7.739999771118164, 7.289999961853027,
				7.53000020980835, 7.300000190734863, 7.210000038146973,
				7.349999904632568, 7.659999847412109, 7.440000057220459,
				7.289999961853027, 7.480000019073486, 7.289999961853027,
				8.180000305175781, 8.180000305175781, 7.460000038146973,
				6.769999980926514, 6.239999771118164, 6.050000190734863,
				5.989999771118164, 6.5, 5.769999980926514, 5.599999904632568,
				5.650000095367432, 5.230000019073486, 4.869999885559082,
				5.139999866485596, 4.139999866485596, 4.699999809265137,
				4.199999809265137, 4.010000228881836, 3.900000095367432,
				4.039999961853027, 4.139999866485596, 4, 4.03000020980835,
				3.9800000190734863, 3.9200000762939458, 3.910000085830689,
				4.429999828338623, 5.090000152587891, 4.840000152587891,
				5.369999885559082, 6.369999885559082, 5.46999979019165,
				5.53000020980835, 5.53000020980835, 5.400000095367432,
				5.429999828338623, 5.5, 5.25, 5.28000020980835, 5.099999904632568,
				5.300000190734863, 6.28000020980835, 6.099999904632568,
				6.309999942779541, 6.940000057220459, 6.119999885559082,
				5.869999885559082, 5.210000038146973, 4.75, 4.550000190734863,
				4.559999942779541, 5.239999771118164, 5.320000171661377,
				5.46999979019165, 6.420000076293945, 6.289999961853027,
				6.130000114440918, 6.309999942779541, 7.78000020980835,
				6.800000190734863, 6.239999771118164, 6.199999809265137,
				6.579999923706055, 6.230000019073486, 6.039999961853027,
				5.739999771118164, 5.559999942779541, 5.440000057220459,
				5.480000019073486, 4.53000020980835, 4.21999979019165,
				4.300000190734863, 4.25, 4.329999923706055, 4.730000019073486,
				4.440000057220459, 4.400000095367432, 4.510000228881836,
				4.510000228881836, 5.139999866485596, 5.110000133514404,
				4.909999847412109, 4.989999771118164, 4.079999923706055,
				4.070000171661377, 4.449999809265137, 4.769999980926514,
				5.449999809265137, 5.619999885559082, 5.480000019073486,
				5.739999771118164, 5.239999771118164, 5.230000019073486,
				4.949999809265137, 5, 4.940000057220459, 4.900000095367432,
				4.940000057220459, 5.179999828338623, 5.21999979019165,
				5.460000038146973, 5.53000020980835, 5.670000076293945,
				5.409999847412109, 5.840000152587891, 6.099999904632568,
				5.829999923706055, 5.809999942779541, 5.599999904632568,
				5.420000076293945, 5.269999980926514, 5.099999904632568,
				5.099999904632568, 4.96999979019165,
			],
			showlegend: false,
			type: "candlestick",
			x: [
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
				"2023-04-24T00:00:00",
				"2023-04-25T00:00:00",
				"2023-04-26T00:00:00",
				"2023-04-27T00:00:00",
				"2023-04-28T00:00:00",
				"2023-05-01T00:00:00",
				"2023-05-02T00:00:00",
				"2023-05-03T00:00:00",
				"2023-05-04T00:00:00",
				"2023-05-05T00:00:00",
				"2023-05-08T00:00:00",
				"2023-05-09T00:00:00",
				"2023-05-10T00:00:00",
				"2023-05-11T00:00:00",
				"2023-05-12T00:00:00",
				"2023-05-15T00:00:00",
				"2023-05-16T00:00:00",
				"2023-05-17T00:00:00",
			],
			xaxis: "x",
			yaxis: "y",
			hoverlabel: {
				namelength: 9,
			},
		},
		{
			marker: {
				color: [
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#e4003a",
					"#00ACFF",
					"#e4003a",
					"#00ACFF",
				],
				line: {
					width: 0.15,
				},
			},
			name: "Volume      ",
			opacity: 0.7,
			type: "bar",
			x: [
				"2020-05-12T00:00:00",
				"2020-05-13T00:00:00",
				"2020-05-14T00:00:00",
				"2020-05-15T00:00:00",
				"2020-05-18T00:00:00",
				"2020-05-19T00:00:00",
				"2020-05-20T00:00:00",
				"2020-05-21T00:00:00",
				"2020-05-22T00:00:00",
				"2020-05-26T00:00:00",
				"2020-05-27T00:00:00",
				"2020-05-28T00:00:00",
				"2020-05-29T00:00:00",
				"2020-06-01T00:00:00",
				"2020-06-02T00:00:00",
				"2020-06-03T00:00:00",
				"2020-06-04T00:00:00",
				"2020-06-05T00:00:00",
				"2020-06-08T00:00:00",
				"2020-06-09T00:00:00",
				"2020-06-10T00:00:00",
				"2020-06-11T00:00:00",
				"2020-06-12T00:00:00",
				"2020-06-15T00:00:00",
				"2020-06-16T00:00:00",
				"2020-06-17T00:00:00",
				"2020-06-18T00:00:00",
				"2020-06-19T00:00:00",
				"2020-06-22T00:00:00",
				"2020-06-23T00:00:00",
				"2020-06-24T00:00:00",
				"2020-06-25T00:00:00",
				"2020-06-26T00:00:00",
				"2020-06-29T00:00:00",
				"2020-06-30T00:00:00",
				"2020-07-01T00:00:00",
				"2020-07-02T00:00:00",
				"2020-07-06T00:00:00",
				"2020-07-07T00:00:00",
				"2020-07-08T00:00:00",
				"2020-07-09T00:00:00",
				"2020-07-10T00:00:00",
				"2020-07-13T00:00:00",
				"2020-07-14T00:00:00",
				"2020-07-15T00:00:00",
				"2020-07-16T00:00:00",
				"2020-07-17T00:00:00",
				"2020-07-20T00:00:00",
				"2020-07-21T00:00:00",
				"2020-07-22T00:00:00",
				"2020-07-23T00:00:00",
				"2020-07-24T00:00:00",
				"2020-07-27T00:00:00",
				"2020-07-28T00:00:00",
				"2020-07-29T00:00:00",
				"2020-07-30T00:00:00",
				"2020-07-31T00:00:00",
				"2020-08-03T00:00:00",
				"2020-08-04T00:00:00",
				"2020-08-05T00:00:00",
				"2020-08-06T00:00:00",
				"2020-08-07T00:00:00",
				"2020-08-10T00:00:00",
				"2020-08-11T00:00:00",
				"2020-08-12T00:00:00",
				"2020-08-13T00:00:00",
				"2020-08-14T00:00:00",
				"2020-08-17T00:00:00",
				"2020-08-18T00:00:00",
				"2020-08-19T00:00:00",
				"2020-08-20T00:00:00",
				"2020-08-21T00:00:00",
				"2020-08-24T00:00:00",
				"2020-08-25T00:00:00",
				"2020-08-26T00:00:00",
				"2020-08-27T00:00:00",
				"2020-08-28T00:00:00",
				"2020-08-31T00:00:00",
				"2020-09-01T00:00:00",
				"2020-09-02T00:00:00",
				"2020-09-03T00:00:00",
				"2020-09-04T00:00:00",
				"2020-09-08T00:00:00",
				"2020-09-09T00:00:00",
				"2020-09-10T00:00:00",
				"2020-09-11T00:00:00",
				"2020-09-14T00:00:00",
				"2020-09-15T00:00:00",
				"2020-09-16T00:00:00",
				"2020-09-17T00:00:00",
				"2020-09-18T00:00:00",
				"2020-09-21T00:00:00",
				"2020-09-22T00:00:00",
				"2020-09-23T00:00:00",
				"2020-09-24T00:00:00",
				"2020-09-25T00:00:00",
				"2020-09-28T00:00:00",
				"2020-09-29T00:00:00",
				"2020-09-30T00:00:00",
				"2020-10-01T00:00:00",
				"2020-10-02T00:00:00",
				"2020-10-05T00:00:00",
				"2020-10-06T00:00:00",
				"2020-10-07T00:00:00",
				"2020-10-08T00:00:00",
				"2020-10-09T00:00:00",
				"2020-10-12T00:00:00",
				"2020-10-13T00:00:00",
				"2020-10-14T00:00:00",
				"2020-10-15T00:00:00",
				"2020-10-16T00:00:00",
				"2020-10-19T00:00:00",
				"2020-10-20T00:00:00",
				"2020-10-21T00:00:00",
				"2020-10-22T00:00:00",
				"2020-10-23T00:00:00",
				"2020-10-26T00:00:00",
				"2020-10-27T00:00:00",
				"2020-10-28T00:00:00",
				"2020-10-29T00:00:00",
				"2020-10-30T00:00:00",
				"2020-11-02T00:00:00",
				"2020-11-03T00:00:00",
				"2020-11-04T00:00:00",
				"2020-11-05T00:00:00",
				"2020-11-06T00:00:00",
				"2020-11-09T00:00:00",
				"2020-11-10T00:00:00",
				"2020-11-11T00:00:00",
				"2020-11-12T00:00:00",
				"2020-11-13T00:00:00",
				"2020-11-16T00:00:00",
				"2020-11-17T00:00:00",
				"2020-11-18T00:00:00",
				"2020-11-19T00:00:00",
				"2020-11-20T00:00:00",
				"2020-11-23T00:00:00",
				"2020-11-24T00:00:00",
				"2020-11-25T00:00:00",
				"2020-11-27T00:00:00",
				"2020-11-30T00:00:00",
				"2020-12-01T00:00:00",
				"2020-12-02T00:00:00",
				"2020-12-03T00:00:00",
				"2020-12-04T00:00:00",
				"2020-12-07T00:00:00",
				"2020-12-08T00:00:00",
				"2020-12-09T00:00:00",
				"2020-12-10T00:00:00",
				"2020-12-11T00:00:00",
				"2020-12-14T00:00:00",
				"2020-12-15T00:00:00",
				"2020-12-16T00:00:00",
				"2020-12-17T00:00:00",
				"2020-12-18T00:00:00",
				"2020-12-21T00:00:00",
				"2020-12-22T00:00:00",
				"2020-12-23T00:00:00",
				"2020-12-24T00:00:00",
				"2020-12-28T00:00:00",
				"2020-12-29T00:00:00",
				"2020-12-30T00:00:00",
				"2020-12-31T00:00:00",
				"2021-01-04T00:00:00",
				"2021-01-05T00:00:00",
				"2021-01-06T00:00:00",
				"2021-01-07T00:00:00",
				"2021-01-08T00:00:00",
				"2021-01-11T00:00:00",
				"2021-01-12T00:00:00",
				"2021-01-13T00:00:00",
				"2021-01-14T00:00:00",
				"2021-01-15T00:00:00",
				"2021-01-19T00:00:00",
				"2021-01-20T00:00:00",
				"2021-01-21T00:00:00",
				"2021-01-22T00:00:00",
				"2021-01-25T00:00:00",
				"2021-01-26T00:00:00",
				"2021-01-27T00:00:00",
				"2021-01-28T00:00:00",
				"2021-01-29T00:00:00",
				"2021-02-01T00:00:00",
				"2021-02-02T00:00:00",
				"2021-02-03T00:00:00",
				"2021-02-04T00:00:00",
				"2021-02-05T00:00:00",
				"2021-02-08T00:00:00",
				"2021-02-09T00:00:00",
				"2021-02-10T00:00:00",
				"2021-02-11T00:00:00",
				"2021-02-12T00:00:00",
				"2021-02-16T00:00:00",
				"2021-02-17T00:00:00",
				"2021-02-18T00:00:00",
				"2021-02-19T00:00:00",
				"2021-02-22T00:00:00",
				"2021-02-23T00:00:00",
				"2021-02-24T00:00:00",
				"2021-02-25T00:00:00",
				"2021-02-26T00:00:00",
				"2021-03-01T00:00:00",
				"2021-03-02T00:00:00",
				"2021-03-03T00:00:00",
				"2021-03-04T00:00:00",
				"2021-03-05T00:00:00",
				"2021-03-08T00:00:00",
				"2021-03-09T00:00:00",
				"2021-03-10T00:00:00",
				"2021-03-11T00:00:00",
				"2021-03-12T00:00:00",
				"2021-03-15T00:00:00",
				"2021-03-16T00:00:00",
				"2021-03-17T00:00:00",
				"2021-03-18T00:00:00",
				"2021-03-19T00:00:00",
				"2021-03-22T00:00:00",
				"2021-03-23T00:00:00",
				"2021-03-24T00:00:00",
				"2021-03-25T00:00:00",
				"2021-03-26T00:00:00",
				"2021-03-29T00:00:00",
				"2021-03-30T00:00:00",
				"2021-03-31T00:00:00",
				"2021-04-01T00:00:00",
				"2021-04-05T00:00:00",
				"2021-04-06T00:00:00",
				"2021-04-07T00:00:00",
				"2021-04-08T00:00:00",
				"2021-04-09T00:00:00",
				"2021-04-12T00:00:00",
				"2021-04-13T00:00:00",
				"2021-04-14T00:00:00",
				"2021-04-15T00:00:00",
				"2021-04-16T00:00:00",
				"2021-04-19T00:00:00",
				"2021-04-20T00:00:00",
				"2021-04-21T00:00:00",
				"2021-04-22T00:00:00",
				"2021-04-23T00:00:00",
				"2021-04-26T00:00:00",
				"2021-04-27T00:00:00",
				"2021-04-28T00:00:00",
				"2021-04-29T00:00:00",
				"2021-04-30T00:00:00",
				"2021-05-03T00:00:00",
				"2021-05-04T00:00:00",
				"2021-05-05T00:00:00",
				"2021-05-06T00:00:00",
				"2021-05-07T00:00:00",
				"2021-05-10T00:00:00",
				"2021-05-11T00:00:00",
				"2021-05-12T00:00:00",
				"2021-05-13T00:00:00",
				"2021-05-14T00:00:00",
				"2021-05-17T00:00:00",
				"2021-05-18T00:00:00",
				"2021-05-19T00:00:00",
				"2021-05-20T00:00:00",
				"2021-05-21T00:00:00",
				"2021-05-24T00:00:00",
				"2021-05-25T00:00:00",
				"2021-05-26T00:00:00",
				"2021-05-27T00:00:00",
				"2021-05-28T00:00:00",
				"2021-06-01T00:00:00",
				"2021-06-02T00:00:00",
				"2021-06-03T00:00:00",
				"2021-06-04T00:00:00",
				"2021-06-07T00:00:00",
				"2021-06-08T00:00:00",
				"2021-06-09T00:00:00",
				"2021-06-10T00:00:00",
				"2021-06-11T00:00:00",
				"2021-06-14T00:00:00",
				"2021-06-15T00:00:00",
				"2021-06-16T00:00:00",
				"2021-06-17T00:00:00",
				"2021-06-18T00:00:00",
				"2021-06-21T00:00:00",
				"2021-06-22T00:00:00",
				"2021-06-23T00:00:00",
				"2021-06-24T00:00:00",
				"2021-06-25T00:00:00",
				"2021-06-28T00:00:00",
				"2021-06-29T00:00:00",
				"2021-06-30T00:00:00",
				"2021-07-01T00:00:00",
				"2021-07-02T00:00:00",
				"2021-07-06T00:00:00",
				"2021-07-07T00:00:00",
				"2021-07-08T00:00:00",
				"2021-07-09T00:00:00",
				"2021-07-12T00:00:00",
				"2021-07-13T00:00:00",
				"2021-07-14T00:00:00",
				"2021-07-15T00:00:00",
				"2021-07-16T00:00:00",
				"2021-07-19T00:00:00",
				"2021-07-20T00:00:00",
				"2021-07-21T00:00:00",
				"2021-07-22T00:00:00",
				"2021-07-23T00:00:00",
				"2021-07-26T00:00:00",
				"2021-07-27T00:00:00",
				"2021-07-28T00:00:00",
				"2021-07-29T00:00:00",
				"2021-07-30T00:00:00",
				"2021-08-02T00:00:00",
				"2021-08-03T00:00:00",
				"2021-08-04T00:00:00",
				"2021-08-05T00:00:00",
				"2021-08-06T00:00:00",
				"2021-08-09T00:00:00",
				"2021-08-10T00:00:00",
				"2021-08-11T00:00:00",
				"2021-08-12T00:00:00",
				"2021-08-13T00:00:00",
				"2021-08-16T00:00:00",
				"2021-08-17T00:00:00",
				"2021-08-18T00:00:00",
				"2021-08-19T00:00:00",
				"2021-08-20T00:00:00",
				"2021-08-23T00:00:00",
				"2021-08-24T00:00:00",
				"2021-08-25T00:00:00",
				"2021-08-26T00:00:00",
				"2021-08-27T00:00:00",
				"2021-08-30T00:00:00",
				"2021-08-31T00:00:00",
				"2021-09-01T00:00:00",
				"2021-09-02T00:00:00",
				"2021-09-03T00:00:00",
				"2021-09-07T00:00:00",
				"2021-09-08T00:00:00",
				"2021-09-09T00:00:00",
				"2021-09-10T00:00:00",
				"2021-09-13T00:00:00",
				"2021-09-14T00:00:00",
				"2021-09-15T00:00:00",
				"2021-09-16T00:00:00",
				"2021-09-17T00:00:00",
				"2021-09-20T00:00:00",
				"2021-09-21T00:00:00",
				"2021-09-22T00:00:00",
				"2021-09-23T00:00:00",
				"2021-09-24T00:00:00",
				"2021-09-27T00:00:00",
				"2021-09-28T00:00:00",
				"2021-09-29T00:00:00",
				"2021-09-30T00:00:00",
				"2021-10-01T00:00:00",
				"2021-10-04T00:00:00",
				"2021-10-05T00:00:00",
				"2021-10-06T00:00:00",
				"2021-10-07T00:00:00",
				"2021-10-08T00:00:00",
				"2021-10-11T00:00:00",
				"2021-10-12T00:00:00",
				"2021-10-13T00:00:00",
				"2021-10-14T00:00:00",
				"2021-10-15T00:00:00",
				"2021-10-18T00:00:00",
				"2021-10-19T00:00:00",
				"2021-10-20T00:00:00",
				"2021-10-21T00:00:00",
				"2021-10-22T00:00:00",
				"2021-10-25T00:00:00",
				"2021-10-26T00:00:00",
				"2021-10-27T00:00:00",
				"2021-10-28T00:00:00",
				"2021-10-29T00:00:00",
				"2021-11-01T00:00:00",
				"2021-11-02T00:00:00",
				"2021-11-03T00:00:00",
				"2021-11-04T00:00:00",
				"2021-11-05T00:00:00",
				"2021-11-08T00:00:00",
				"2021-11-09T00:00:00",
				"2021-11-10T00:00:00",
				"2021-11-11T00:00:00",
				"2021-11-12T00:00:00",
				"2021-11-15T00:00:00",
				"2021-11-16T00:00:00",
				"2021-11-17T00:00:00",
				"2021-11-18T00:00:00",
				"2021-11-19T00:00:00",
				"2021-11-22T00:00:00",
				"2021-11-23T00:00:00",
				"2021-11-24T00:00:00",
				"2021-11-26T00:00:00",
				"2021-11-29T00:00:00",
				"2021-11-30T00:00:00",
				"2021-12-01T00:00:00",
				"2021-12-02T00:00:00",
				"2021-12-03T00:00:00",
				"2021-12-06T00:00:00",
				"2021-12-07T00:00:00",
				"2021-12-08T00:00:00",
				"2021-12-09T00:00:00",
				"2021-12-10T00:00:00",
				"2021-12-13T00:00:00",
				"2021-12-14T00:00:00",
				"2021-12-15T00:00:00",
				"2021-12-16T00:00:00",
				"2021-12-17T00:00:00",
				"2021-12-20T00:00:00",
				"2021-12-21T00:00:00",
				"2021-12-22T00:00:00",
				"2021-12-23T00:00:00",
				"2021-12-27T00:00:00",
				"2021-12-28T00:00:00",
				"2021-12-29T00:00:00",
				"2021-12-30T00:00:00",
				"2021-12-31T00:00:00",
				"2022-01-03T00:00:00",
				"2022-01-04T00:00:00",
				"2022-01-05T00:00:00",
				"2022-01-06T00:00:00",
				"2022-01-07T00:00:00",
				"2022-01-10T00:00:00",
				"2022-01-11T00:00:00",
				"2022-01-12T00:00:00",
				"2022-01-13T00:00:00",
				"2022-01-14T00:00:00",
				"2022-01-18T00:00:00",
				"2022-01-19T00:00:00",
				"2022-01-20T00:00:00",
				"2022-01-21T00:00:00",
				"2022-01-24T00:00:00",
				"2022-01-25T00:00:00",
				"2022-01-26T00:00:00",
				"2022-01-27T00:00:00",
				"2022-01-28T00:00:00",
				"2022-01-31T00:00:00",
				"2022-02-01T00:00:00",
				"2022-02-02T00:00:00",
				"2022-02-03T00:00:00",
				"2022-02-04T00:00:00",
				"2022-02-07T00:00:00",
				"2022-02-08T00:00:00",
				"2022-02-09T00:00:00",
				"2022-02-10T00:00:00",
				"2022-02-11T00:00:00",
				"2022-02-14T00:00:00",
				"2022-02-15T00:00:00",
				"2022-02-16T00:00:00",
				"2022-02-17T00:00:00",
				"2022-02-18T00:00:00",
				"2022-02-22T00:00:00",
				"2022-02-23T00:00:00",
				"2022-02-24T00:00:00",
				"2022-02-25T00:00:00",
				"2022-02-28T00:00:00",
				"2022-03-01T00:00:00",
				"2022-03-02T00:00:00",
				"2022-03-03T00:00:00",
				"2022-03-04T00:00:00",
				"2022-03-07T00:00:00",
				"2022-03-08T00:00:00",
				"2022-03-09T00:00:00",
				"2022-03-10T00:00:00",
				"2022-03-11T00:00:00",
				"2022-03-14T00:00:00",
				"2022-03-15T00:00:00",
				"2022-03-16T00:00:00",
				"2022-03-17T00:00:00",
				"2022-03-18T00:00:00",
				"2022-03-21T00:00:00",
				"2022-03-22T00:00:00",
				"2022-03-23T00:00:00",
				"2022-03-24T00:00:00",
				"2022-03-25T00:00:00",
				"2022-03-28T00:00:00",
				"2022-03-29T00:00:00",
				"2022-03-30T00:00:00",
				"2022-03-31T00:00:00",
				"2022-04-01T00:00:00",
				"2022-04-04T00:00:00",
				"2022-04-05T00:00:00",
				"2022-04-06T00:00:00",
				"2022-04-07T00:00:00",
				"2022-04-08T00:00:00",
				"2022-04-11T00:00:00",
				"2022-04-12T00:00:00",
				"2022-04-13T00:00:00",
				"2022-04-14T00:00:00",
				"2022-04-18T00:00:00",
				"2022-04-19T00:00:00",
				"2022-04-20T00:00:00",
				"2022-04-21T00:00:00",
				"2022-04-22T00:00:00",
				"2022-04-25T00:00:00",
				"2022-04-26T00:00:00",
				"2022-04-27T00:00:00",
				"2022-04-28T00:00:00",
				"2022-04-29T00:00:00",
				"2022-05-02T00:00:00",
				"2022-05-03T00:00:00",
				"2022-05-04T00:00:00",
				"2022-05-05T00:00:00",
				"2022-05-06T00:00:00",
				"2022-05-09T00:00:00",
				"2022-05-10T00:00:00",
				"2022-05-11T00:00:00",
				"2022-05-12T00:00:00",
				"2022-05-13T00:00:00",
				"2022-05-16T00:00:00",
				"2022-05-17T00:00:00",
				"2022-05-18T00:00:00",
				"2022-05-19T00:00:00",
				"2022-05-20T00:00:00",
				"2022-05-23T00:00:00",
				"2022-05-24T00:00:00",
				"2022-05-25T00:00:00",
				"2022-05-26T00:00:00",
				"2022-05-27T00:00:00",
				"2022-05-31T00:00:00",
				"2022-06-01T00:00:00",
				"2022-06-02T00:00:00",
				"2022-06-03T00:00:00",
				"2022-06-06T00:00:00",
				"2022-06-07T00:00:00",
				"2022-06-08T00:00:00",
				"2022-06-09T00:00:00",
				"2022-06-10T00:00:00",
				"2022-06-13T00:00:00",
				"2022-06-14T00:00:00",
				"2022-06-15T00:00:00",
				"2022-06-16T00:00:00",
				"2022-06-17T00:00:00",
				"2022-06-21T00:00:00",
				"2022-06-22T00:00:00",
				"2022-06-23T00:00:00",
				"2022-06-24T00:00:00",
				"2022-06-27T00:00:00",
				"2022-06-28T00:00:00",
				"2022-06-29T00:00:00",
				"2022-06-30T00:00:00",
				"2022-07-01T00:00:00",
				"2022-07-05T00:00:00",
				"2022-07-06T00:00:00",
				"2022-07-07T00:00:00",
				"2022-07-08T00:00:00",
				"2022-07-11T00:00:00",
				"2022-07-12T00:00:00",
				"2022-07-13T00:00:00",
				"2022-07-14T00:00:00",
				"2022-07-15T00:00:00",
				"2022-07-18T00:00:00",
				"2022-07-19T00:00:00",
				"2022-07-20T00:00:00",
				"2022-07-21T00:00:00",
				"2022-07-22T00:00:00",
				"2022-07-25T00:00:00",
				"2022-07-26T00:00:00",
				"2022-07-27T00:00:00",
				"2022-07-28T00:00:00",
				"2022-07-29T00:00:00",
				"2022-08-01T00:00:00",
				"2022-08-02T00:00:00",
				"2022-08-03T00:00:00",
				"2022-08-04T00:00:00",
				"2022-08-05T00:00:00",
				"2022-08-08T00:00:00",
				"2022-08-09T00:00:00",
				"2022-08-10T00:00:00",
				"2022-08-11T00:00:00",
				"2022-08-12T00:00:00",
				"2022-08-15T00:00:00",
				"2022-08-16T00:00:00",
				"2022-08-17T00:00:00",
				"2022-08-18T00:00:00",
				"2022-08-19T00:00:00",
				"2022-08-22T00:00:00",
				"2022-08-23T00:00:00",
				"2022-08-24T00:00:00",
				"2022-08-25T00:00:00",
				"2022-08-26T00:00:00",
				"2022-08-29T00:00:00",
				"2022-08-30T00:00:00",
				"2022-08-31T00:00:00",
				"2022-09-01T00:00:00",
				"2022-09-02T00:00:00",
				"2022-09-06T00:00:00",
				"2022-09-07T00:00:00",
				"2022-09-08T00:00:00",
				"2022-09-09T00:00:00",
				"2022-09-12T00:00:00",
				"2022-09-13T00:00:00",
				"2022-09-14T00:00:00",
				"2022-09-15T00:00:00",
				"2022-09-16T00:00:00",
				"2022-09-19T00:00:00",
				"2022-09-20T00:00:00",
				"2022-09-21T00:00:00",
				"2022-09-22T00:00:00",
				"2022-09-23T00:00:00",
				"2022-09-26T00:00:00",
				"2022-09-27T00:00:00",
				"2022-09-28T00:00:00",
				"2022-09-29T00:00:00",
				"2022-09-30T00:00:00",
				"2022-10-03T00:00:00",
				"2022-10-04T00:00:00",
				"2022-10-05T00:00:00",
				"2022-10-06T00:00:00",
				"2022-10-07T00:00:00",
				"2022-10-10T00:00:00",
				"2022-10-11T00:00:00",
				"2022-10-12T00:00:00",
				"2022-10-13T00:00:00",
				"2022-10-14T00:00:00",
				"2022-10-17T00:00:00",
				"2022-10-18T00:00:00",
				"2022-10-19T00:00:00",
				"2022-10-20T00:00:00",
				"2022-10-21T00:00:00",
				"2022-10-24T00:00:00",
				"2022-10-25T00:00:00",
				"2022-10-26T00:00:00",
				"2022-10-27T00:00:00",
				"2022-10-28T00:00:00",
				"2022-10-31T00:00:00",
				"2022-11-01T00:00:00",
				"2022-11-02T00:00:00",
				"2022-11-03T00:00:00",
				"2022-11-04T00:00:00",
				"2022-11-07T00:00:00",
				"2022-11-08T00:00:00",
				"2022-11-09T00:00:00",
				"2022-11-10T00:00:00",
				"2022-11-11T00:00:00",
				"2022-11-14T00:00:00",
				"2022-11-15T00:00:00",
				"2022-11-16T00:00:00",
				"2022-11-17T00:00:00",
				"2022-11-18T00:00:00",
				"2022-11-21T00:00:00",
				"2022-11-22T00:00:00",
				"2022-11-23T00:00:00",
				"2022-11-25T00:00:00",
				"2022-11-28T00:00:00",
				"2022-11-29T00:00:00",
				"2022-11-30T00:00:00",
				"2022-12-01T00:00:00",
				"2022-12-02T00:00:00",
				"2022-12-05T00:00:00",
				"2022-12-06T00:00:00",
				"2022-12-07T00:00:00",
				"2022-12-08T00:00:00",
				"2022-12-09T00:00:00",
				"2022-12-12T00:00:00",
				"2022-12-13T00:00:00",
				"2022-12-14T00:00:00",
				"2022-12-15T00:00:00",
				"2022-12-16T00:00:00",
				"2022-12-19T00:00:00",
				"2022-12-20T00:00:00",
				"2022-12-21T00:00:00",
				"2022-12-22T00:00:00",
				"2022-12-23T00:00:00",
				"2022-12-27T00:00:00",
				"2022-12-28T00:00:00",
				"2022-12-29T00:00:00",
				"2022-12-30T00:00:00",
				"2023-01-03T00:00:00",
				"2023-01-04T00:00:00",
				"2023-01-05T00:00:00",
				"2023-01-06T00:00:00",
				"2023-01-09T00:00:00",
				"2023-01-10T00:00:00",
				"2023-01-11T00:00:00",
				"2023-01-12T00:00:00",
				"2023-01-13T00:00:00",
				"2023-01-17T00:00:00",
				"2023-01-18T00:00:00",
				"2023-01-19T00:00:00",
				"2023-01-20T00:00:00",
				"2023-01-23T00:00:00",
				"2023-01-24T00:00:00",
				"2023-01-25T00:00:00",
				"2023-01-26T00:00:00",
				"2023-01-27T00:00:00",
				"2023-01-30T00:00:00",
				"2023-01-31T00:00:00",
				"2023-02-01T00:00:00",
				"2023-02-02T00:00:00",
				"2023-02-03T00:00:00",
				"2023-02-06T00:00:00",
				"2023-02-07T00:00:00",
				"2023-02-08T00:00:00",
				"2023-02-09T00:00:00",
				"2023-02-10T00:00:00",
				"2023-02-13T00:00:00",
				"2023-02-14T00:00:00",
				"2023-02-15T00:00:00",
				"2023-02-16T00:00:00",
				"2023-02-17T00:00:00",
				"2023-02-21T00:00:00",
				"2023-02-22T00:00:00",
				"2023-02-23T00:00:00",
				"2023-02-24T00:00:00",
				"2023-02-27T00:00:00",
				"2023-02-28T00:00:00",
				"2023-03-01T00:00:00",
				"2023-03-02T00:00:00",
				"2023-03-03T00:00:00",
				"2023-03-06T00:00:00",
				"2023-03-07T00:00:00",
				"2023-03-08T00:00:00",
				"2023-03-09T00:00:00",
				"2023-03-10T00:00:00",
				"2023-03-13T00:00:00",
				"2023-03-14T00:00:00",
				"2023-03-15T00:00:00",
				"2023-03-16T00:00:00",
				"2023-03-17T00:00:00",
				"2023-03-20T00:00:00",
				"2023-03-21T00:00:00",
				"2023-03-22T00:00:00",
				"2023-03-23T00:00:00",
				"2023-03-24T00:00:00",
				"2023-03-27T00:00:00",
				"2023-03-28T00:00:00",
				"2023-03-29T00:00:00",
				"2023-03-30T00:00:00",
				"2023-03-31T00:00:00",
				"2023-04-03T00:00:00",
				"2023-04-04T00:00:00",
				"2023-04-05T00:00:00",
				"2023-04-06T00:00:00",
				"2023-04-10T00:00:00",
				"2023-04-11T00:00:00",
				"2023-04-12T00:00:00",
				"2023-04-13T00:00:00",
				"2023-04-14T00:00:00",
				"2023-04-17T00:00:00",
				"2023-04-18T00:00:00",
				"2023-04-19T00:00:00",
				"2023-04-20T00:00:00",
				"2023-04-21T00:00:00",
				"2023-04-24T00:00:00",
				"2023-04-25T00:00:00",
				"2023-04-26T00:00:00",
				"2023-04-27T00:00:00",
				"2023-04-28T00:00:00",
				"2023-05-01T00:00:00",
				"2023-05-02T00:00:00",
				"2023-05-03T00:00:00",
				"2023-05-04T00:00:00",
				"2023-05-05T00:00:00",
				"2023-05-08T00:00:00",
				"2023-05-09T00:00:00",
				"2023-05-10T00:00:00",
				"2023-05-11T00:00:00",
				"2023-05-12T00:00:00",
				"2023-05-15T00:00:00",
				"2023-05-16T00:00:00",
				"2023-05-17T00:00:00",
			],
			xaxis: "x",
			xhoverformat: "%Y-%m-%d",
			y: [
				19681900, 10433400, 5920500, 3483200, 6036900, 4929800, 3489500,
				4555000, 2408800, 8864100, 7862400, 6590200, 5153000, 3913100, 6877800,
				15353700, 8572600, 9240900, 8587100, 7394300, 15322600, 9179400,
				7352900, 5541400, 5911900, 3030800, 5619700, 13137400, 4412800, 5699300,
				8104100, 8251400, 5601000, 9321600, 3653400, 4914500, 3908400, 5489700,
				2667600, 5586800, 4225700, 3306100, 4464800, 3291400, 4830700, 1997500,
				2101700, 2393100, 1925900, 2798800, 3614300, 3282400, 2980500, 6226600,
				5489400, 3699100, 2584900, 4047100, 3267800, 1691500, 3742600, 24917400,
				5048200, 5794800, 2457900, 23259200, 12055800, 7159200, 5790700,
				5728300, 13728400, 8216900, 8498200, 5296900, 4419400, 31921200,
				9882700, 9820200, 9524000, 43056200, 10626600, 9328800, 6667400,
				5578200, 5881800, 4863000, 4638100, 5099300, 4380600, 2619700, 2428500,
				4563200, 2090100, 4285200, 4569700, 2375500, 4676500, 2520000, 2515400,
				3223800, 3576000, 9468800, 8404200, 6917700, 7868500, 5021400, 3245700,
				10164700, 31709400, 9808600, 18565800, 40385100, 15978800, 7277300,
				12339600, 6635000, 10458800, 11251300, 8141100, 9019300, 10968800,
				11180100, 15441900, 7609100, 8056200, 9049500, 132511000, 42129300,
				24066100, 14729000, 21995400, 47604300, 22483000, 31717300, 13986400,
				17088800, 31514600, 62884600, 22647300, 10097400, 16555000, 12130100,
				11847600, 66080900, 33157300, 20503900, 29530600, 20991200, 19872800,
				22310400, 67159000, 54432100, 25423200, 23799300, 21941700, 22699800,
				21638400, 15724800, 11094200, 23942700, 21086100, 40278400, 28234300,
				29873800, 28148300, 67363300, 26150500, 39553300, 41695800, 41549200,
				45847700, 49638800, 162356400, 256276000, 181862200, 64823800,
				268273400, 443238100, 456850200, 1222342500, 591223900, 602193300,
				434608000, 462775900, 221405100, 162985800, 197097600, 128171500,
				102588100, 152810800, 55920400, 46773000, 61165700, 38849000, 130540800,
				40249100, 173409000, 264876400, 376881800, 445717400, 137028000,
				143586500, 78135400, 55651900, 77822600, 59734100, 114343800, 150415600,
				261918600, 83933600, 111146700, 277713300, 125967600, 78053600,
				121418000, 153206000, 88760100, 87923200, 81850700, 131192800, 84633100,
				37330700, 39020500, 29832300, 77473900, 96082300, 44067000, 28804300,
				33408700, 29254600, 51269800, 44049100, 51166300, 45198900, 40696700,
				32814300, 27008900, 23598200, 49923500, 27465600, 78592900, 51629800,
				39720500, 37782900, 27741000, 31251200, 35222400, 27608700, 39586300,
				38245000, 41015300, 49601000, 54423500, 296525000, 207589900, 158933100,
				172488400, 89024100, 61419700, 53937700, 113319200, 213644100,
				379064100, 705545700, 660623600, 508694600, 766462500, 598142200,
				337710100, 349094900, 214490300, 150361300, 224860600, 218006600,
				301467300, 285582100, 166450700, 303576000, 243645900, 185876100,
				169494100, 116291800, 80351200, 77596900, 99310200, 63604100, 59020600,
				57549900, 90271500, 62370300, 95320200, 145078200, 57858600, 62986400,
				86807300, 137830700, 199584500, 126825700, 112891300, 168673400,
				158023700, 93985900, 85474800, 97977600, 55288900, 71021400, 59446200,
				52996200, 59061700, 61740100, 85496600, 108565100, 52858800, 81054600,
				116181500, 69684300, 71050000, 57990800, 86506600, 86868900, 57948000,
				47920600, 55432600, 75319300, 228489600, 209271500, 109710200, 72507400,
				108370900, 127659600, 82772400, 67848800, 52109500, 102133700, 97372900,
				85960900, 90152700, 75111000, 65850300, 76214200, 57581900, 68549000,
				70807600, 64696300, 52441300, 41271100, 32555900, 31225500, 39523300,
				43007800, 102819800, 65919000, 39943200, 37702800, 35450500, 37704300,
				29643100, 37351400, 25223400, 33484500, 67688200, 46524700, 50096300,
				52769200, 31220000, 33977600, 46324200, 30905400, 27674800, 25904100,
				23812200, 32841500, 34054600, 44361700, 82198000, 28553700, 40077000,
				70347800, 37893500, 32463600, 30474100, 23623100, 39918800, 38388200,
				23322000, 25262000, 20692300, 28831800, 27386500, 18270800, 19731800,
				24063800, 41354800, 63296100, 54901300, 66188600, 45067400, 47927100,
				46241500, 36556800, 58858900, 84199800, 107045800, 53208200, 43702400,
				144753100, 66938200, 46852500, 36983800, 37005900, 30785600, 31588100,
				30983400, 36056400, 23408000, 26740900, 33347900, 45172100, 59112700,
				49481000, 37784000, 36063800, 27472100, 41005000, 56996600, 55679700,
				35096800, 51078700, 65185700, 82424700, 42434600, 76722900, 50530200,
				53951600, 41447900, 124427700, 48626000, 39215100, 32396500, 29755200,
				51272500, 75867400, 98957400, 68425900, 46106700, 39852000, 49444600,
				40130200, 31953500, 42968500, 30155900, 54405700, 36944100, 42772300,
				44003000, 35038200, 24825900, 39474600, 39507300, 35370400, 25206400,
				25666500, 29202900, 32959800, 40853100, 39104500, 24130400, 31992100,
				34256700, 81798900, 170142600, 68471700, 71814700, 226704100, 212293100,
				95384200, 89239000, 65735700, 51458400, 41624100, 52212200, 53370500,
				42674700, 37554600, 42073100, 36748800, 25333700, 23892600, 31744900,
				23913500, 26372700, 26431700, 26444200, 24732800, 26605900, 29857700,
				21342700, 23883200, 31834300, 26421700, 26987600, 33033100, 40732200,
				55693400, 58508700, 104887800, 58129300, 40960000, 38183500, 53729400,
				50443600, 41387900, 24854400, 37689300, 53628000, 71002400, 89906000,
				106975100, 55539700, 45309900, 39226000, 34537900, 40370600, 51742200,
				32254800, 26509900, 27916000, 25723500, 37213600, 26518100, 33042300,
				30357400, 26660700, 36073100, 80759000, 77601900, 39677100, 27690400,
				25348300, 30952600, 30261000, 25762100, 62763200, 48920800, 32394400,
				52835300, 36666700, 35302700, 26144400, 54040500, 50521500, 47582900,
				39350000, 31469900, 20684400, 21317100, 23192000, 24618700, 18222500,
				23919700, 42734200, 41951900, 66585500, 125780200, 132819200, 62368000,
				64157000, 79337900, 73386000, 50182700, 72301100, 59412000, 50429000,
				52461500, 151158700, 80188100, 50478300, 31596300, 36395500, 39602600,
				25167500, 33009700, 26335600, 27614700, 20866600, 21748200, 16708600,
				36352800, 47225600, 24911200, 22078200, 25060300, 31989600, 23541600,
				17186900, 20213400, 26927300, 19910100, 43284800, 33360100, 24459500,
				22502000, 16557000, 21499900, 42548300, 22699700, 19345600, 23436200,
				23392700, 22621500, 32491800, 29565500, 21898500, 18580700, 22041400,
				13666100, 17309400, 14944400, 14363100, 26798200, 22177600, 13873300,
				15668600, 34859100, 21354300, 22112800, 16543200, 18134600, 20037900,
				27189800, 25711200, 40052500, 43822300, 53083900, 44349800, 29929700,
				17120000, 21572100, 16892700, 14591900, 37509000, 8287600, 17873200,
				16317800, 44913800, 96708500, 34765600, 28313400, 26188100, 39234400,
				30496400, 23156400, 17523800, 34932400, 22155900, 28706500, 29694500,
				29491400, 22273800, 19607600, 55461400, 30311100, 21676500, 29744300,
				21225300, 18450800, 22100500, 17999300, 12757400, 15305400, 17164000,
				12844600, 53995000, 41961200, 25920300, 57607200, 62125400, 29734900,
				31271900, 34092900, 24293900, 22975100, 21597700, 33469600, 34116800,
				21881400, 36970900, 49690900, 52353400, 62513900, 47411000, 31424400,
				29113300, 38670800, 26219400, 36199200, 46423100, 32085500, 27592400,
				73513000, 68667200, 42144300, 23398200, 122677600, 113458500, 41177400,
				28191200, 34069600, 26242800, 16229900, 19862100, 16178500, 15574700,
				15518100, 64871300, 36323300, 27931700, 27433600, 17581300, 20847100,
				31446600, 19451500, 10477600, 20314400, 89811500, 28097800, 20233500,
				19003300, 33616600, 90399500, 30657200, 101554800, 50575700, 36630400,
				33620800, 20485400, 35998000, 16213100, 13732300, 12814000, 10295800,
				13973200, 22384400, 33038200, 14275100, 21015100, 18975600, 20341200,
				16239700, 25057200, 25007500, 28341000, 17036100, 18772800, 14568400,
				13891600, 13330900, 12115100, 11852900, 9485846,
			],
			yaxis: "y2",
			hoverlabel: {
				namelength: 6,
			},
		},
	],
	layout: {
		annotations: [
			{
				font: {
					color: "gray",
					size: 24,
				},
				opacity: 0.5,
				text: "",
				textangle: -90,
				x: 0,
				xanchor: "left",
				xref: "paper",
				xshift: -110,
				y: 0.5,
				yanchor: "middle",
				yref: "paper",
			},
		],
		hoverdistance: 2,
		margin: {
			autoexpand: true,
			b: 85,
			l: 120,
			pad: 0,
			r: 50,
			t: 40,
		},
		modebar: {
			activecolor: "#d1030d",
			bgcolor: "#2A2A2A",
			color: "#FFFFFF",
			orientation: "v",
		},
		newshape: {
			line: {
				color: "gold",
			},
		},
		showlegend: false,
		spikedistance: 2,
		template: {
			data: {
				bar: [
					{
						error_x: {
							color: "#f2f5fa",
						},
						error_y: {
							color: "#f2f5fa",
						},
						marker: {
							line: {
								color: "rgb(17,17,17)",
								width: 0.5,
							},
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "bar",
					},
				],
				barpolar: [
					{
						marker: {
							line: {
								color: "rgb(17,17,17)",
								width: 0.5,
							},
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "barpolar",
					},
				],
				candlestick: [
					{
						decreasing: {
							fillcolor: "#e4003a",
							line: {
								color: "#e4003a",
							},
						},
						increasing: {
							fillcolor: "#00ACFF",
							line: {
								color: "#00ACFF",
							},
						},
						type: "candlestick",
					},
				],
				carpet: [
					{
						aaxis: {
							endlinecolor: "#A2B1C6",
							gridcolor: "#506784",
							linecolor: "#506784",
							minorgridcolor: "#506784",
							startlinecolor: "#A2B1C6",
						},
						baxis: {
							endlinecolor: "#A2B1C6",
							gridcolor: "#506784",
							linecolor: "#506784",
							minorgridcolor: "#506784",
							startlinecolor: "#A2B1C6",
						},
						type: "carpet",
					},
				],
				choropleth: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "choropleth",
					},
				],
				contour: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "contour",
					},
				],
				contourcarpet: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "contourcarpet",
					},
				],
				heatmap: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "heatmap",
					},
				],
				heatmapgl: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "heatmapgl",
					},
				],
				histogram: [
					{
						marker: {
							pattern: {
								fillmode: "overlay",
								size: 10,
								solidity: 0.2,
							},
						},
						type: "histogram",
					},
				],
				histogram2d: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "histogram2d",
					},
				],
				histogram2dcontour: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "histogram2dcontour",
					},
				],
				mesh3d: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						type: "mesh3d",
					},
				],
				parcoords: [
					{
						line: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "parcoords",
					},
				],
				pie: [
					{
						automargin: true,
						type: "pie",
					},
				],
				scatter: [
					{
						marker: {
							line: {
								color: "#283442",
							},
						},
						type: "scatter",
					},
				],
				scatter3d: [
					{
						line: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatter3d",
					},
				],
				scattercarpet: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattercarpet",
					},
				],
				scattergeo: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattergeo",
					},
				],
				scattergl: [
					{
						marker: {
							line: {
								color: "#283442",
							},
						},
						type: "scattergl",
					},
				],
				scattermapbox: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scattermapbox",
					},
				],
				scatterpolar: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterpolar",
					},
				],
				scatterpolargl: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterpolargl",
					},
				],
				scatterternary: [
					{
						marker: {
							colorbar: {
								outlinewidth: 0,
								ticks: "",
							},
						},
						type: "scatterternary",
					},
				],
				surface: [
					{
						colorbar: {
							outlinewidth: 0,
							ticks: "",
						},
						colorscale: [
							[0, "#0d0887"],
							[0.1111111111111111, "#46039f"],
							[0.2222222222222222, "#7201a8"],
							[0.3333333333333333, "#9c179e"],
							[0.4444444444444444, "#bd3786"],
							[0.5555555555555556, "#d8576b"],
							[0.6666666666666666, "#ed7953"],
							[0.7777777777777778, "#fb9f3a"],
							[0.8888888888888888, "#fdca26"],
							[1, "#f0f921"],
						],
						type: "surface",
					},
				],
				table: [
					{
						cells: {
							fill: {
								color: "#506784",
							},
							line: {
								color: "rgb(17,17,17)",
							},
						},
						header: {
							fill: {
								color: "#2a3f5f",
							},
							line: {
								color: "rgb(17,17,17)",
							},
						},
						type: "table",
					},
				],
			},
			layout: {
				annotationdefaults: {
					arrowcolor: "#f2f5fa",
					arrowhead: 0,
					arrowwidth: 1,
					showarrow: false,
				},
				autotypenumbers: "strict",
				coloraxis: {
					colorbar: {
						outlinewidth: 0,
						ticks: "",
					},
				},
				colorscale: {
					diverging: [
						[0, "#8e0152"],
						[0.1, "#c51b7d"],
						[0.2, "#de77ae"],
						[0.3, "#f1b6da"],
						[0.4, "#fde0ef"],
						[0.5, "#f7f7f7"],
						[0.6, "#e6f5d0"],
						[0.7, "#b8e186"],
						[0.8, "#7fbc41"],
						[0.9, "#4d9221"],
						[1, "#276419"],
					],
					sequential: [
						[0, "#0d0887"],
						[0.1111111111111111, "#46039f"],
						[0.2222222222222222, "#7201a8"],
						[0.3333333333333333, "#9c179e"],
						[0.4444444444444444, "#bd3786"],
						[0.5555555555555556, "#d8576b"],
						[0.6666666666666666, "#ed7953"],
						[0.7777777777777778, "#fb9f3a"],
						[0.8888888888888888, "#fdca26"],
						[1, "#f0f921"],
					],
					sequentialminus: [
						[0, "#0d0887"],
						[0.1111111111111111, "#46039f"],
						[0.2222222222222222, "#7201a8"],
						[0.3333333333333333, "#9c179e"],
						[0.4444444444444444, "#bd3786"],
						[0.5555555555555556, "#d8576b"],
						[0.6666666666666666, "#ed7953"],
						[0.7777777777777778, "#fb9f3a"],
						[0.8888888888888888, "#fdca26"],
						[1, "#f0f921"],
					],
				},
				colorway: [
					"#ffed00",
					"#ef7d00",
					"#e4003a",
					"#c13246",
					"#822661",
					"#48277c",
					"#005ca9",
					"#00aaff",
					"#9b30d9",
					"#af005f",
					"#5f00af",
					"#af87ff",
				],
				dragmode: "pan",
				font: {
					color: "#f2f5fa",
					family: "Fira Code",
					size: 18,
				},
				geo: {
					bgcolor: "rgb(17,17,17)",
					lakecolor: "rgb(17,17,17)",
					landcolor: "rgb(17,17,17)",
					showlakes: true,
					showland: true,
					subunitcolor: "#506784",
				},
				hoverlabel: {
					align: "left",
				},
				hovermode: "x",
				legend: {
					bgcolor: "rgba(0, 0, 0, 0)",
					font: {
						size: 15,
					},
					x: 0.01,
					xanchor: "left",
					y: 0.99,
					yanchor: "top",
				},
				mapbox: {
					style: "dark",
				},
				paper_bgcolor: "#000000",
				plot_bgcolor: "#000000",
				polar: {
					angularaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					bgcolor: "rgb(17,17,17)",
					radialaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
				},
				scene: {
					xaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
					yaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
					zaxis: {
						backgroundcolor: "rgb(17,17,17)",
						gridcolor: "#506784",
						gridwidth: 2,
						linecolor: "#506784",
						showbackground: true,
						ticks: "",
						zerolinecolor: "#C8D4E3",
					},
				},
				shapedefaults: {
					line: {
						color: "#f2f5fa",
					},
				},
				sliderdefaults: {
					bgcolor: "#C8D4E3",
					bordercolor: "rgb(17,17,17)",
					borderwidth: 1,
					tickwidth: 0,
				},
				ternary: {
					aaxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					baxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
					bgcolor: "rgb(17,17,17)",
					caxis: {
						gridcolor: "#506784",
						linecolor: "#506784",
						ticks: "",
					},
				},
				title: {
					x: 0.05,
				},
				updatemenudefaults: {
					bgcolor: "#506784",
					borderwidth: 0,
				},
				xaxis: {
					automargin: true,
					autorange: true,
					gridcolor: "#283442",
					linecolor: "#F5EFF3",
					mirror: true,
					rangeslider: {
						visible: false,
					},
					showgrid: true,
					showline: true,
					tick0: 1,
					tickfont: {
						size: 14,
					},
					ticks: "outside",
					title: {
						standoff: 20,
					},
					zeroline: false,
					zerolinecolor: "#283442",
					zerolinewidth: 2,
				},
				yaxis: {
					anchor: "x",
					automargin: true,
					fixedrange: false,
					gridcolor: "#283442",
					linecolor: "#F5EFF3",
					mirror: true,
					showgrid: true,
					showline: true,
					side: "right",
					tick0: 0.5,
					ticks: "outside",
					title: {
						standoff: 20,
					},
					zeroline: false,
					zerolinecolor: "#283442",
					zerolinewidth: 2,
				},
			},
		},
		title: {
			text: "",
			x: 0.5,
			xanchor: "center",
			y: 0.98,
			yanchor: "top",
		},
		xaxis: {
			anchor: "y",
			domain: [0, 0.94],
			matches: "x3",
			rangebreaks: [
				{
					bounds: ["sat", "mon"],
				},
				{
					values: [
						"2021-11-25T00:00:00",
						"2021-01-01T00:00:00",
						"2020-05-25T00:00:00",
						"2022-04-15T00:00:00",
						"2022-12-26T00:00:00",
						"2020-12-25T00:00:00",
						"2022-06-20T00:00:00",
						"2021-04-02T00:00:00",
						"2022-02-21T00:00:00",
						"2023-04-07T00:00:00",
						"2022-01-17T00:00:00",
						"2021-07-05T00:00:00",
						"2020-11-26T00:00:00",
						"2021-01-18T00:00:00",
						"2021-02-15T00:00:00",
						"2022-11-24T00:00:00",
						"2020-09-07T00:00:00",
						"2022-09-05T00:00:00",
						"2020-07-03T00:00:00",
						"2022-07-04T00:00:00",
						"2023-02-20T00:00:00",
						"2022-05-30T00:00:00",
						"2021-05-31T00:00:00",
						"2021-09-06T00:00:00",
						"2021-12-24T00:00:00",
						"2023-01-02T00:00:00",
						"2023-01-16T00:00:00",
					],
				},
			],
			showticklabels: true,
			type: "date",
			rangeslider: {
				yaxis: {},
				yaxis2: {},
			},
			range: ["2020-05-11 12:00", "2023-05-17 12:00"],
			autorange: true,
		},
		yaxis: {
			anchor: "x",
			automargin: true,
			autorange: true,
			domain: [0, 1],
			fixedrange: false,
			layer: "above traces",
			nticks: 15,
			side: "right",
			tickfont: {
				size: 16,
			},
			type: "linear",
			range: [-2.0183335211541924, 76.54833623435762],
		},
		yaxis2: {
			anchor: "x",
			fixedrange: true,
			nticks: 10,
			overlaying: "y",
			range: [0, 8556397500],
			showgrid: false,
			showline: false,
			side: "left",
			tickfont: {
				size: 13,
			},
			tickvals: [200000000, 400000000, 600000000, 800000000],
			zeroline: false,
			type: "linear",
		},
		xaxis3: {
			range: ["2020-05-11 12:00", "2023-05-17 12:00"],
			autorange: true,
		},
	},
	python_version: "3.10.11",
	pywry_version: "0.5.8",
	terminal_version: "3.0.1",
	theme: "dark",
};
