{"compilerOptions": {"target": "ESNext", "typeRoots": ["node_modules/@types"], "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src", "../fonts"], "references": [{"path": "./tsconfig.node.json"}]}