{"name": "tables", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build_tsc": "tsc && vite build", "deploy": "npm run build && mv dist/index.html ../../openbb_platform/obbject_extensions/charting/openbb_charting/core/table.html", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-checkbox": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.3", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.4", "@radix-ui/react-icons": "^1.2.0", "@radix-ui/react-radio-group": "^1.1.2", "@radix-ui/react-select": "^1.2.1", "@radix-ui/react-toast": "^1.1.3", "@tanstack/match-sorter-utils": "^8.7.6", "@tanstack/react-table": "^8.7.9", "@tanstack/react-virtual": "^3.13.9", "dom-to-image": "^2.6.0", "esbuild": ">=0.25.0", "nanoid": ">=3.3.8", "plotly.js": "^3.0.1", "react": "^18.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.0.0", "react-plotly.js": "^2.6.0", "react-table": "^7.8.0", "rollup": ">=4.22.4", "xss": "^1.0.14", "brace-expansion": ">=2.0.2"}, "devDependencies": {"@types/dom-to-image": "^2.6.4", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/react-table": "^7.7.14", "@types/wicg-file-system-access": "^2020.9.6", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.13", "clsx": "^1.2.1", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^4.9.3", "vite": ">=6.2.7", "vite-plugin-singlefile": "^0.13.3"}}