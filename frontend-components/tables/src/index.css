@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  ._input {
    @apply w-full text-xs px-2 py-1 bg-grey-50 dark:bg-grey-900 h-[25px] border-[1.5px] border-grey-300 dark:border-grey-700 rounded outline-none dark:outline-1 focus:border-grey-500 active:border-grey-500 dark:text-white dark:focus:text-white dark:focus:border-white dark:active:border-white dark:hover:border-white disabled:border-grey-600 font-normal;
  }
  ._btn-common {
    @apply whitespace-nowrap inline-flex w-full items-center justify-center gap-2 rounded py-2 px-6 text-center !no-underline transition duration-150 ease-out md:w-fit;
  }
  ._btn {
    @apply _btn-common text-sm bg-grey-800 text-white hover:bg-grey-600 dark:bg-grey-100 dark:text-grey-800 dark:hover:bg-white focus:outline focus:outline-2 focus:outline-grey-500 dark:active:bg-grey-200 dark:hover:active:text-grey-900 disabled:bg-grey-300 disabled:text-grey-500 active:disabled:bg-grey-300 active:disabled:text-grey-500;
  }
  ._btn-secondary {
    @apply _btn-common border border-grey-300 text-grey-300 outline-offset-0 hover:border-white hover:text-white focus:outline focus:outline-2 focus:outline-grey-500 active:border-white active:bg-grey-800 active:text-white disabled:border-grey-500 disabled:bg-transparent disabled:text-grey-500;
  }
  ._btn-tertiary {
    @apply _btn-common bg-grey-900 text-grey-50 hover:bg-grey-800 hover:text-white focus:bg-grey-900 focus:outline focus:outline-2 focus:outline-grey-500 active:bg-grey-700 active:text-white disabled:bg-grey-500 disabled:text-grey-700;
  }
  ._icon-btn {
    @apply _btn-common bg-grey-900 text-grey-50 hover:bg-grey-800 hover:text-white focus:bg-grey-900 focus:outline focus:outline-2 focus:outline-grey-500 active:bg-grey-700 active:text-white disabled:bg-grey-500 disabled:text-grey-700;
  }
  ._hyper-link {
    @apply underline text-blue-300 hover:text-blue-400 active:text-blue-500 disabled:text-grey-600;
  }
  ._modal {
    @apply card fixed top-1/2 left-1/2 z-50 max-h-[608px] w-[90%] max-w-[568px] -translate-x-1/2 -translate-y-1/2 overflow-auto text-white bg-grey-900 p-2 duration-300 focus:outline-none md:p-10;
  }
  ._modal-overlay {
    @apply bg-grey-900 opacity-60 z-40 inset-0 fixed;
  }
  ._modal-title {
    @apply text-white uppercase tracking-widest text-lg font-bold;
  }
  .card {
    @apply w-full rounded border border-grey-600 p-6 shadow bg-grey-850;
  }
}

._header {
  background: #062d48;
  background: linear-gradient(
    90deg,
    #062d48,
    #0b3054 15%,
    #0e386c 45%,
    #0b203d 64%,
    #06101a 82%,
    #060709
  );
}

body {
  font-family: "Arial", monospace;
}

table {
  width: 100%;
  table-layout: auto;
}

table thead,
table tfoot,
table tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

th, td {
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resizer {
  cursor: col-resize;
  user-select: none;
  touch-action: none;
  transition: opacity 0.2s ease-in-out;
}

.resizer.isResizing {
  background: white;
  opacity: 1;
}

@media (hover: hover) {
  .resizer {
    opacity: 0;
  }

  *:hover > .resizer {
    opacity: 1;
  }
}

/* custom scrollbar */
::-webkit-scrollbar {
  width: 20px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #d6dee1;
  border-radius: 20px;
  border: 6px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8bbbf;
}

::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0);
}

table thead th {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.saving {
  position: absolute;
  z-index: 9999999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 100px;
  width: 200px;
  display: none;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(250, 250, 250, 0.5);
  font-size: 0.9em;
  border-radius: 20px;
  animation: popup 0.3s ease-in-out;
}

.saving.show {
  display: flex;
}
.loader {
  position: relative;
  bottom: 0px;
  right: -5px;
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid #00acff;
  width: 20px;
  height: 20px;
  animation: spin 1.5s linear infinite;
  opacity: 1;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
