# Pull Request Template for OpenBB Developers

0. **Title**:

    - Format: [Type] - Brief Description (e.g., [Hotfix] - Improve Calculation Accuracy).

1. **Why**? (1-3 sentences or a bullet point list):

    - State the primary reason for this change.

    - Example: "To enhance the accuracy of our risk calculation in response to recent market volatility."

2. **What**? (1-3 sentences or a bullet point list):

    - Describe what has been done in simple terms.

    - Example: "Updated the risk calculation algorithm to factor in real-time market fluctuations."

3. **Impact** (1-2 sentences or a bullet point list):

    - Briefly note the expected outcome or any potential risks and share the Impact Analysis score.

    - Example: "Expected to improve risk assessment accuracy by 15%, with minimal performance impact. Impact score: 10"

    > [!TIP]
    > Refer to the Impact Analysis confluence (internal) document for more information.

4. **Testing Done**:

    - A quick note on how it was tested.

    - Example: "Validated with historical market data and simulated scenarios."

5. **Reviewer Notes** (optional):

    - Any specific focus areas for review?

    - Example: "Please check algorithm compatibility with existing data models."

6. **Any other information** (optional)
