# Pull Request the OpenBB Platform CLI

## Description

- [ ] Summary of the change/ bug fix.
- [ ] Link # issue, if applicable.
- [ ] Screenshot of the feature or the bug before/after fix, if applicable.
- [ ] Relevant motivation and context.
- [ ] List any dependencies that are required for this change.

## How has this been tested?

- Please describe the tests that you ran to verify your changes.
- Please provide instructions so we can reproduce.
- Please also list any relevant details for your test configuration.

- [ ] Ensure the affected commands still execute in the OpenBB Platform CLI.
- [ ] Ensure the Platform is working as intended.
- [ ] Check any related reports.

## Checklist

- [ ] I ensure I have self-reviewed my code.
- [ ] I have commented/documented my code, particularly in hard-to-understand sections.
- [ ] I have adhered to the GitFlow naming convention and my branch name is in the format of `feature/feature-name` or `hotfix/hotfix-name`.
- [ ] Update [our documentation](https://openbb-finance.github.io/OpenBBTerminal/) following [these guidelines](https://github.com/OpenBB-finance/OpenBB/tree/main/website).  Update any user guides that are affected by the changes.
- [ ] Update our tests following [these guidelines](https://github.com/OpenBB-finance/OpenBB/tree/main/tests).
- [ ] Make sure you are following our [CONTRIBUTING guidelines](https://github.com/OpenBB-finance/OpenBB/blob/main/CONTRIBUTING.md).
- [ ] If a feature was added make sure to add it to the corresponding [integration test script](https://github.com/OpenBB-finance/OpenBB/tree/develop/openbb_terminal/miscellaneous/integration_tests_scripts).
